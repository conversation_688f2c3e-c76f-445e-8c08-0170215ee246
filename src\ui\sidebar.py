"""
Sidebar component for ArchPDF - Shows bookmarks and thumbnails.
"""

import tkinter as tk
import customtkinter as ctk
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class SideBar(ctk.CTkFrame):
    """Sidebar with bookmarks and thumbnails."""
    
    def __init__(self, parent, app):
        """
        Initialize the modern sidebar.

        Args:
            parent: Parent widget
            app: Main application instance
        """
        # Use Adobe Reader-style frame styling
        super().__init__(
            parent,
            fg_color="#f3f4f6",      # Light gray background
            border_width=1,
            border_color="#d1d5db",  # Light gray border
            corner_radius=0
        )
        self.app = app

        # Configure modern size
        self.configure(width=280)

        # Create modern sidebar content
        self._create_modern_content()

        # Current tab
        self.current_tab = "bookmarks"
    
    def _create_modern_content(self):
        """Create modern sidebar content with elegant tabs."""
        # Configure grid with proper spacing
        self.grid_rowconfigure(1, weight=1)
        self.grid_columnconfigure(0, weight=1)

        # Adobe Reader-style tab header
        self.tab_frame = ctk.CTkFrame(
            self,
            fg_color="#e5e7eb",      # Medium light gray
            border_width=0,
            corner_radius=0,
            height=50
        )
        self.tab_frame.grid(row=0, column=0, sticky="ew", padx=0, pady=0)
        self.tab_frame.grid_columnconfigure(0, weight=1)
        self.tab_frame.grid_columnconfigure(1, weight=1)
        self.tab_frame.grid_propagate(False)

        # Adobe Reader-style tab buttons with professional gray styling
        self.bookmarks_tab = ctk.CTkButton(
            self.tab_frame,
            text="⬜ Bookmarks",      # Professional icon
            command=lambda: self._switch_tab("bookmarks"),
            width=130,
            height=36,
            fg_color="#6b7280",      # Professional gray
            hover_color="#9ca3af",   # Lighter gray on hover
            text_color="#ffffff",
            corner_radius=6,         # Slightly less rounded
            font=("Segoe UI", 11, "normal"),
            border_width=0
        )
        self.bookmarks_tab.grid(row=0, column=0, padx=(8, 4), pady=7, sticky="ew")

        self.thumbnails_tab = ctk.CTkButton(
            self.tab_frame,
            text="⬜ Thumbnails",     # Professional icon
            command=lambda: self._switch_tab("thumbnails"),
            width=130,
            height=36,
            fg_color="#f3f4f6",      # Light gray (inactive)
            hover_color="#e5e7eb",   # Slightly darker on hover
            text_color="#6b7280",    # Medium gray text
            corner_radius=6,         # Slightly less rounded
            font=("Segoe UI", 11, "normal"),
            border_width=0
        )
        self.thumbnails_tab.grid(row=0, column=1, padx=(4, 8), pady=7, sticky="ew")

        # Adobe Reader-style content area
        self.content_frame = ctk.CTkFrame(
            self,
            fg_color="#ffffff",      # White background
            border_width=0,
            corner_radius=0
        )
        self.content_frame.grid(row=1, column=0, sticky="nsew", padx=0, pady=0)
        self.content_frame.grid_rowconfigure(0, weight=1)
        self.content_frame.grid_columnconfigure(0, weight=1)

        # Create modern views
        self._create_modern_bookmarks_view()
        self._create_modern_thumbnails_view()

        # Show default view
        self._switch_tab("bookmarks")
    
    def _create_modern_bookmarks_view(self):
        """Create Adobe Reader-style bookmarks view."""
        self.bookmarks_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            fg_color="#ffffff",      # White background
            border_width=0,
            corner_radius=0,
            scrollbar_fg_color="#f3f4f6",      # Light gray scrollbar
            scrollbar_button_color="#d1d5db",  # Light gray button
            scrollbar_button_hover_color="#9ca3af"  # Medium gray hover
        )

        # Adobe Reader-style placeholder
        self.bookmarks_placeholder = ctk.CTkLabel(
            self.bookmarks_frame,
            text="⬜\n\nNo bookmarks available\n\nBookmarks will appear here\nwhen you open a PDF document",
            text_color="#6b7280",    # Medium gray text
            font=("Segoe UI", 11, "normal"),
            justify="center"
        )
        self.bookmarks_placeholder.pack(pady=40, padx=20)

    def _create_modern_thumbnails_view(self):
        """Create Adobe Reader-style thumbnails view."""
        self.thumbnails_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            fg_color="#ffffff",      # White background
            border_width=0,
            corner_radius=0,
            scrollbar_fg_color="#f3f4f6",      # Light gray scrollbar
            scrollbar_button_color="#d1d5db",  # Light gray button
            scrollbar_button_hover_color="#9ca3af"  # Medium gray hover
        )

        # Adobe Reader-style placeholder
        self.thumbnails_placeholder = ctk.CTkLabel(
            self.thumbnails_frame,
            text="⬜\n\nNo thumbnails available\n\nPage thumbnails will appear here\nwhen you open a PDF document",
            text_color="#6b7280",    # Medium gray text
            font=("Segoe UI", 11, "normal"),
            justify="center"
        )
        self.thumbnails_placeholder.pack(pady=40, padx=20)
    
    def _switch_tab(self, tab_name: str):
        """
        Switch to a different tab with modern styling.

        Args:
            tab_name: Name of the tab to switch to
        """
        self.current_tab = tab_name

        # Hide all views
        self.bookmarks_frame.grid_remove()
        self.thumbnails_frame.grid_remove()

        # Update button styles with Adobe Reader-style colors
        if tab_name == "bookmarks":
            # Active bookmarks tab - professional gray
            self.bookmarks_tab.configure(
                fg_color="#6b7280",      # Professional gray
                hover_color="#9ca3af",   # Lighter gray on hover
                text_color="#ffffff"     # White text
            )
            # Inactive thumbnails tab - light gray
            self.thumbnails_tab.configure(
                fg_color="#f3f4f6",      # Light gray
                hover_color="#e5e7eb",   # Slightly darker on hover
                text_color="#6b7280"     # Medium gray text
            )
            self.bookmarks_frame.grid(row=0, column=0, sticky="nsew", padx=0, pady=0)
        else:
            # Inactive bookmarks tab - light gray
            self.bookmarks_tab.configure(
                fg_color="#f3f4f6",      # Light gray
                hover_color="#e5e7eb",   # Slightly darker on hover
                text_color="#6b7280"     # Medium gray text
            )
            # Active thumbnails tab - professional gray
            self.thumbnails_tab.configure(
                fg_color="#6b7280",      # Professional gray
                hover_color="#9ca3af",   # Lighter gray on hover
                text_color="#ffffff"     # White text
            )
            self.thumbnails_frame.grid(row=0, column=0, sticky="nsew", padx=0, pady=0)
    
    def load_document(self):
        """Load document content into sidebar."""
        if not self.app.pdf_engine.is_document_loaded():
            self.clear()
            return
        
        # Load bookmarks
        self._load_bookmarks()
        
        # Load thumbnails (placeholder for now)
        self._load_thumbnails()
    
    def _load_bookmarks(self):
        """Load document bookmarks."""
        # Clear existing bookmarks
        for widget in self.bookmarks_frame.winfo_children():
            if widget != self.bookmarks_placeholder:
                widget.destroy()
        
        # Get bookmarks from PDF
        bookmarks = self.app.pdf_engine.get_bookmarks()
        
        if bookmarks:
            self.bookmarks_placeholder.pack_forget()
            
            for bookmark in bookmarks:
                self._create_bookmark_item(bookmark)
        else:
            self.bookmarks_placeholder.pack(pady=20)
    
    def _create_bookmark_item(self, bookmark: Dict[str, Any]):
        """
        Create a modern bookmark item with elegant styling.

        Args:
            bookmark: Bookmark information
        """
        level = bookmark.get('level', 1)
        title = bookmark.get('title', 'Untitled')
        page = bookmark.get('page', 0)

        # Create modern bookmark button with visual hierarchy
        indent = "  " * (level - 1)

        # Add level-based professional icon
        level_icon = "⬜" if level == 1 else "▷" if level == 2 else "•"
        text = f"{indent}{level_icon} {title}"

        bookmark_btn = ctk.CTkButton(
            self.bookmarks_frame,
            text=text,
            anchor="w",
            command=lambda p=page: self.app.go_to_page(p + 1),  # Convert to 1-based
            width=240,
            height=32,
            fg_color="transparent",
            hover_color="#f3f4f6",   # Light gray hover
            text_color="#374151",    # Dark gray text
            corner_radius=6,
            font=("Segoe UI", 10, "normal"),
            border_width=0
        )
        bookmark_btn.pack(fill="x", padx=8, pady=2)

        # Add Adobe Reader-style hover effect
        def on_enter(event):
            bookmark_btn.configure(
                fg_color="#f3f4f6",      # Light gray background
                text_color="#374151"     # Dark gray text
            )

        def on_leave(event):
            bookmark_btn.configure(
                fg_color="transparent",
                text_color="#374151"     # Dark gray text
            )

        bookmark_btn.bind("<Enter>", on_enter)
        bookmark_btn.bind("<Leave>", on_leave)
    
    def _load_thumbnails(self):
        """Load page thumbnails (placeholder implementation)."""
        # Clear existing thumbnails
        for widget in self.thumbnails_frame.winfo_children():
            if widget != self.thumbnails_placeholder:
                widget.destroy()
        
        # For now, just show placeholder
        # In a full implementation, this would generate thumbnail images
        if self.app.pdf_engine.is_document_loaded():
            self.thumbnails_placeholder.configure(text="Thumbnails coming soon...")
        else:
            self.thumbnails_placeholder.configure(text="No thumbnails available")
        
        self.thumbnails_placeholder.pack(pady=20)
    
    def clear(self):
        """Clear sidebar content."""
        # Clear bookmarks
        for widget in self.bookmarks_frame.winfo_children():
            if widget != self.bookmarks_placeholder:
                widget.destroy()
        
        # Clear thumbnails
        for widget in self.thumbnails_frame.winfo_children():
            if widget != self.thumbnails_placeholder:
                widget.destroy()
        
        # Show placeholders
        self.bookmarks_placeholder.pack(pady=20)
        self.thumbnails_placeholder.pack(pady=20)
        
        # Reset placeholder text
        self.bookmarks_placeholder.configure(text="No bookmarks available")
        self.thumbnails_placeholder.configure(text="No thumbnails available")
