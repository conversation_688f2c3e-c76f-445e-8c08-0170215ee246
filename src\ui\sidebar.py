"""
Sidebar component for ArchPDF - Shows bookmarks and thumbnails.
"""

import tkinter as tk
import customtkinter as ctk
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class SideBar(ctk.CTkFrame):
    """Sidebar with bookmarks and thumbnails."""
    
    def __init__(self, parent, app):
        """
        Initialize the Adobe Reader DC exact sidebar.

        Args:
            parent: Parent widget
            app: Main application instance
        """
        # Adobe Reader DC exact sidebar styling - FORCE COLORS
        super().__init__(parent)
        self.app = app

        # FORCE Adobe Reader DC exact colors immediately with multiple methods
        self.configure(
            fg_color="#f0f0f0",      # Adobe Reader exact sidebar background
            border_width=0,          # No border like Adobe Reader
            corner_radius=0,         # No corner radius like Adobe Reader
            width=240                # Adobe Reader DC exact sidebar width
        )

        # Schedule color forcing after widget is fully created
        self.after_idle(self._force_sidebar_colors)

        # Create modern sidebar content
        self._create_modern_content()

        # Current tab
        self.current_tab = "bookmarks"

    def _force_sidebar_colors(self):
        """Force Adobe Reader colors on sidebar after creation."""
        try:
            self.configure(fg_color="#f0f0f0")
            self.update_idletasks()
            logger.debug("Sidebar colors forced")
        except Exception as e:
            logger.warning(f"Could not force sidebar colors: {e}")

    def _create_modern_content(self):
        """Create modern sidebar content with elegant tabs."""
        # Configure grid with proper spacing
        self.grid_rowconfigure(1, weight=1)
        self.grid_columnconfigure(0, weight=1)

        # Adobe Reader DC exact tab header
        self.tab_frame = ctk.CTkFrame(
            self,
            fg_color="#e5e5e5",      # Adobe Reader exact tab header background
            border_width=0,
            corner_radius=0,
            height=40                # Adobe Reader exact tab height
        )
        self.tab_frame.grid(row=0, column=0, sticky="ew", padx=0, pady=0)
        self.tab_frame.grid_columnconfigure(0, weight=1)
        self.tab_frame.grid_columnconfigure(1, weight=1)
        self.tab_frame.grid_propagate(False)

        # Adobe Reader DC exact tab buttons
        self.bookmarks_tab = ctk.CTkButton(
            self.tab_frame,
            text="📑 Bookmarks",      # Adobe Reader style icon
            command=lambda: self._switch_tab("bookmarks"),
            width=115,
            height=28,
            fg_color="#5a5a5a",      # Adobe Reader exact active tab gray
            hover_color="#707070",   # Adobe Reader exact hover gray
            text_color="#ffffff",    # White text for active tab
            corner_radius=3,         # Adobe Reader exact corner radius
            font=("Segoe UI", 10, "normal"),
            border_width=0
        )
        self.bookmarks_tab.grid(row=0, column=0, padx=(4, 2), pady=6, sticky="ew")

        self.thumbnails_tab = ctk.CTkButton(
            self.tab_frame,
            text="🖼️ Thumbnails",     # Adobe Reader style icon
            command=lambda: self._switch_tab("thumbnails"),
            width=115,
            height=28,
            fg_color="#f0f0f0",      # Adobe Reader exact inactive tab background
            hover_color="#e8e8e8",   # Adobe Reader exact hover
            text_color="#5a5a5a",    # Adobe Reader exact text color
            corner_radius=3,         # Adobe Reader exact corner radius
            font=("Segoe UI", 10, "normal"),
            border_width=1,
            border_color="#d0d0d0"   # Adobe Reader exact border
        )
        self.thumbnails_tab.grid(row=0, column=1, padx=(2, 4), pady=6, sticky="ew")

        # Adobe Reader DC exact content area
        self.content_frame = ctk.CTkFrame(
            self,
            fg_color="#f8f8f8",      # Adobe Reader exact content background
            border_width=0,
            corner_radius=0
        )
        self.content_frame.grid(row=1, column=0, sticky="nsew", padx=0, pady=0)
        self.content_frame.grid_rowconfigure(0, weight=1)
        self.content_frame.grid_columnconfigure(0, weight=1)

        # Create modern views
        self._create_modern_bookmarks_view()
        self._create_modern_thumbnails_view()

        # Show default view
        self._switch_tab("bookmarks")
    
    def _create_modern_bookmarks_view(self):
        """Create Adobe Reader DC exact bookmarks view."""
        self.bookmarks_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            fg_color="#f8f8f8",      # Adobe Reader exact background
            border_width=0,
            corner_radius=0,
            scrollbar_fg_color="#e5e5e5",      # Adobe Reader exact scrollbar
            scrollbar_button_color="#d0d0d0",  # Adobe Reader exact button
            scrollbar_button_hover_color="#c0c0c0"  # Adobe Reader exact hover
        )

        # Adobe Reader DC exact placeholder
        self.bookmarks_placeholder = ctk.CTkLabel(
            self.bookmarks_frame,
            text="📑\n\nNo bookmarks available\n\nBookmarks will appear here\nwhen you open a PDF document",
            text_color="#5a5a5a",    # Adobe Reader exact medium gray text
            font=("Segoe UI", 11, "normal"),
            justify="center"
        )
        self.bookmarks_placeholder.pack(pady=40, padx=20)

    def _create_modern_thumbnails_view(self):
        """Create Adobe Reader DC exact thumbnails view."""
        self.thumbnails_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            fg_color="#f8f8f8",      # Adobe Reader exact background
            border_width=0,
            corner_radius=0,
            scrollbar_fg_color="#e5e5e5",      # Adobe Reader exact scrollbar
            scrollbar_button_color="#d0d0d0",  # Adobe Reader exact button
            scrollbar_button_hover_color="#c0c0c0"  # Adobe Reader exact hover
        )

        # Adobe Reader DC exact placeholder
        self.thumbnails_placeholder = ctk.CTkLabel(
            self.thumbnails_frame,
            text="🖼️\n\nNo thumbnails available\n\nPage thumbnails will appear here\nwhen you open a PDF document",
            text_color="#5a5a5a",    # Adobe Reader exact medium gray text
            font=("Segoe UI", 11, "normal"),
            justify="center"
        )
        self.thumbnails_placeholder.pack(pady=40, padx=20)
    
    def _switch_tab(self, tab_name: str):
        """
        Switch to a different tab with modern styling.

        Args:
            tab_name: Name of the tab to switch to
        """
        self.current_tab = tab_name

        # Hide all views
        self.bookmarks_frame.grid_remove()
        self.thumbnails_frame.grid_remove()

        # Update button styles with Adobe Reader DC exact colors
        if tab_name == "bookmarks":
            # Active bookmarks tab - Adobe Reader exact
            self.bookmarks_tab.configure(
                fg_color="#5a5a5a",      # Adobe Reader exact active tab gray
                hover_color="#707070",   # Adobe Reader exact hover gray
                text_color="#ffffff",    # White text for active tab
                border_width=0
            )
            # Inactive thumbnails tab - Adobe Reader exact
            self.thumbnails_tab.configure(
                fg_color="#f0f0f0",      # Adobe Reader exact inactive background
                hover_color="#e8e8e8",   # Adobe Reader exact hover
                text_color="#5a5a5a",    # Adobe Reader exact text color
                border_width=1,
                border_color="#d0d0d0"   # Adobe Reader exact border
            )
            self.bookmarks_frame.grid(row=0, column=0, sticky="nsew", padx=0, pady=0)
        else:
            # Inactive bookmarks tab - Adobe Reader exact
            self.bookmarks_tab.configure(
                fg_color="#f0f0f0",      # Adobe Reader exact inactive background
                hover_color="#e8e8e8",   # Adobe Reader exact hover
                text_color="#5a5a5a",    # Adobe Reader exact text color
                border_width=1,
                border_color="#d0d0d0"   # Adobe Reader exact border
            )
            # Active thumbnails tab - Adobe Reader exact
            self.thumbnails_tab.configure(
                fg_color="#5a5a5a",      # Adobe Reader exact active tab gray
                hover_color="#707070",   # Adobe Reader exact hover gray
                text_color="#ffffff",    # White text for active tab
                border_width=0
            )
            self.thumbnails_frame.grid(row=0, column=0, sticky="nsew", padx=0, pady=0)
    
    def load_document(self):
        """Load document content into sidebar."""
        if not self.app.pdf_engine.is_document_loaded():
            self.clear()
            return
        
        # Load bookmarks
        self._load_bookmarks()
        
        # Load thumbnails (placeholder for now)
        self._load_thumbnails()
    
    def _load_bookmarks(self):
        """Load document bookmarks."""
        # Clear existing bookmarks
        for widget in self.bookmarks_frame.winfo_children():
            if widget != self.bookmarks_placeholder:
                widget.destroy()
        
        # Get bookmarks from PDF
        bookmarks = self.app.pdf_engine.get_bookmarks()
        
        if bookmarks:
            self.bookmarks_placeholder.pack_forget()
            
            for bookmark in bookmarks:
                self._create_bookmark_item(bookmark)
        else:
            self.bookmarks_placeholder.pack(pady=20)
    
    def _create_bookmark_item(self, bookmark: Dict[str, Any]):
        """
        Create a modern bookmark item with elegant styling.

        Args:
            bookmark: Bookmark information
        """
        level = bookmark.get('level', 1)
        title = bookmark.get('title', 'Untitled')
        page = bookmark.get('page', 0)

        # Create modern bookmark button with visual hierarchy
        indent = "  " * (level - 1)

        # Add level-based professional icon
        level_icon = "⬜" if level == 1 else "▷" if level == 2 else "•"
        text = f"{indent}{level_icon} {title}"

        bookmark_btn = ctk.CTkButton(
            self.bookmarks_frame,
            text=text,
            anchor="w",
            command=lambda p=page: self.app.go_to_page(p + 1),  # Convert to 1-based
            width=240,
            height=32,
            fg_color="transparent",
            hover_color="#f3f4f6",   # Light gray hover
            text_color="#374151",    # Dark gray text
            corner_radius=6,
            font=("Segoe UI", 10, "normal"),
            border_width=0
        )
        bookmark_btn.pack(fill="x", padx=8, pady=2)

        # Add Adobe Reader-style hover effect
        def on_enter(event):
            bookmark_btn.configure(
                fg_color="#f3f4f6",      # Light gray background
                text_color="#374151"     # Dark gray text
            )

        def on_leave(event):
            bookmark_btn.configure(
                fg_color="transparent",
                text_color="#374151"     # Dark gray text
            )

        bookmark_btn.bind("<Enter>", on_enter)
        bookmark_btn.bind("<Leave>", on_leave)
    
    def _load_thumbnails(self):
        """Load page thumbnails (placeholder implementation)."""
        # Clear existing thumbnails
        for widget in self.thumbnails_frame.winfo_children():
            if widget != self.thumbnails_placeholder:
                widget.destroy()
        
        # For now, just show placeholder
        # In a full implementation, this would generate thumbnail images
        if self.app.pdf_engine.is_document_loaded():
            self.thumbnails_placeholder.configure(text="Thumbnails coming soon...")
        else:
            self.thumbnails_placeholder.configure(text="No thumbnails available")
        
        self.thumbnails_placeholder.pack(pady=20)
    
    def clear(self):
        """Clear sidebar content."""
        # Clear bookmarks
        for widget in self.bookmarks_frame.winfo_children():
            if widget != self.bookmarks_placeholder:
                widget.destroy()
        
        # Clear thumbnails
        for widget in self.thumbnails_frame.winfo_children():
            if widget != self.thumbnails_placeholder:
                widget.destroy()
        
        # Show placeholders
        self.bookmarks_placeholder.pack(pady=20)
        self.thumbnails_placeholder.pack(pady=20)
        
        # Reset placeholder text
        self.bookmarks_placeholder.configure(text="No bookmarks available")
        self.thumbnails_placeholder.configure(text="No thumbnails available")
