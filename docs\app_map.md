# ArchPDF - Mappa dell'Applicazione

## Panoramica del Progetto
**ArchPDF** è un lettore PDF professionale per Windows con tema scuro moderno e icone bicolore.

### Informazioni Generali
- **Nome**: ArchPDF
- **Versione**: 1.0.0
- **Autore**: Augment Agent
- **Linguaggio**: Python 3.x
- **Framework GUI**: CustomTkinter + Tkinter
- **Motore PDF**: PyMuPDF (fitz)
- **Piattaforma**: Windows

## Struttura delle Cartelle

```
archpdf_v2/
├── main.py                 # Entry point principale dell'applicazione
├── requirements.txt        # Dipendenze Python
├── ArchPDF.spec           # Configurazione PyInstaller
├── build_exe.py           # Script per build eseguibile
├── create_app_icon.py     # Script per creazione icone
├── run_tests.py           # Runner per i test
├── test_ui.py             # Test interfaccia utente
│
├── docs/                  # Documentazione
│   └── app_map.md         # Questo file - mappa dell'app
│
├── assets/                # Risorse statiche
│   └── icons/             # Icone dell'applicazione
│
├── src/                   # Codice sorgente principale
│   ├── __init__.py
│   ├── app/               # Logica applicazione principale
│   │   ├── __init__.py
│   │   ├── config.py      # Configurazioni e costanti
│   │   ├── main_window.py # Finestra principale
│   │   └── settings.py    # Gestione impostazioni
│   │
│   ├── pdf/               # Motore PDF e funzionalità correlate
│   │   ├── __init__.py
│   │   ├── engine.py      # Motore PDF core (PyMuPDF)
│   │   ├── search.py      # Funzionalità di ricerca
│   │   └── viewer.py      # Visualizzatore PDF
│   │
│   ├── ui/                # Componenti interfaccia utente
│   │   ├── __init__.py
│   │   ├── menubar.py     # Barra menu
│   │   ├── toolbar.py     # Barra strumenti
│   │   ├── statusbar.py   # Barra stato
│   │   ├── sidebar.py     # Pannello laterale
│   │   ├── theme.py       # Gestione temi
│   │   └── search_dialog.py # Dialog di ricerca
│   │
│   └── utils/             # Utilità e helper
│       ├── __init__.py
│       ├── file_manager.py    # Gestione file
│       ├── settings_manager.py # Gestione impostazioni
│       ├── icon_manager.py    # Gestione icone
│       └── error_handler.py   # Gestione errori
│
├── tests/                 # Test unitari
│   ├── __init__.py
│   ├── test_file_manager.py
│   └── test_pdf_engine.py
│
├── build/                 # File di build
└── dist/                  # Distribuzione finale
```

## Componenti Principali

### 1. Entry Point (main.py)
- Punto di ingresso dell'applicazione
- Gestisce l'importazione dei moduli
- Avvia l'applicazione principale

### 2. Applicazione Core (src/app/)
- **main_window.py**: Finestra principale con layout e gestione eventi
- **config.py**: Configurazioni, costanti e tema scuro
- **settings.py**: Gestione persistenza impostazioni

### 3. Motore PDF (src/pdf/)
- **engine.py**: Core PDF usando PyMuPDF per rendering e manipolazione
- **search.py**: Funzionalità di ricerca nel testo
- **viewer.py**: Componente visualizzazione PDF

### 4. Interfaccia Utente (src/ui/)
- **menubar.py**: Menu principale dell'applicazione
- **toolbar.py**: Barra strumenti con controlli rapidi
- **statusbar.py**: Barra stato con informazioni documento
- **sidebar.py**: Pannello laterale con bookmarks e navigazione
- **theme.py**: Gestione tema scuro e colori
- **search_dialog.py**: Dialog per ricerca testo

### 5. Utilità (src/utils/)
- **file_manager.py**: Gestione file e file recenti
- **settings_manager.py**: Persistenza configurazioni
- **icon_manager.py**: Gestione icone bicolore
- **error_handler.py**: Gestione errori e dialog

## Dipendenze Principali

### GUI Framework
- **customtkinter**: Framework GUI moderno
- **tkinter**: Framework GUI base Python

### PDF Processing
- **PyMuPDF (fitz)**: Motore PDF per rendering e manipolazione

### Image Processing
- **Pillow**: Elaborazione immagini

### Packaging
- **pyinstaller**: Creazione eseguibile standalone

## Funzionalità Implementate

### Core Features
- ✅ Apertura e visualizzazione PDF
- ✅ Navigazione pagine (avanti/indietro)
- ✅ Controlli zoom (in/out/fit)
- ✅ Ricerca testo nel documento
- ✅ Bookmarks e navigazione outline
- ✅ File recenti

### UI Features
- ✅ Tema scuro moderno
- ✅ Icone bicolore
- ✅ Sidebar con bookmarks
- ✅ Barra strumenti
- ✅ Barra stato
- ✅ Scorciatoie tastiera

### Advanced Features
- ✅ Gestione errori robusta
- ✅ Progress dialog per file grandi
- ✅ Persistenza impostazioni
- ✅ Fullscreen mode
- ✅ Gestione file manager

## Configurazione

### Impostazioni Default
- Tema: Scuro
- Zoom: 100%
- Modalità fit: Fit Width
- Finestra: 1200x800
- Sidebar: Visibile (250px)

### Colori Tema Scuro
- Background primario: #1e1e1e
- Background secondario: #2d2d2d
- Testo primario: #ffffff
- Accent primario: #007acc (blu)
- Accent secondario: #ff6b35 (arancione)

## File di Configurazione
- Impostazioni salvate in: `~/.archpdf/`
- Formato: JSON
- Auto-save al chiusura applicazione

## Test e Build

### Test
- Test unitari in `/tests/`
- Runner: `run_tests.py`
- Framework: pytest

### Build Eseguibile
- Script: `build_exe.py`
- Tool: PyInstaller
- Configurazione: `ArchPDF.spec`
- Output: `/dist/ArchPDF.exe`

## Note Tecniche

### Architettura
- Pattern MVC con separazione responsabilità
- Gestione eventi centralizzata
- Error handling robusto
- Logging integrato

### Performance
- Rendering lazy delle pagine
- Cache immagini
- Progress dialog per operazioni lunghe
- Gestione memoria ottimizzata

### Sicurezza
- Validazione file PDF
- Gestione eccezioni
- Controllo permessi file

## Aggiornamenti Recenti

### 2025-06-17 - REPLICA ESATTA ADOBE READER DC COMPLETATA ✅

#### FASE 1: PALETTE COLORI ADOBE READER DC ESATTA ✅
- ✅ **Colori Background**: Implementati colori esatti Adobe Reader (#f8f8f8, #e5e5e5, #f0f0f0)
- ✅ **Colori Testo**: Utilizzati colori esatti Adobe Reader (#2c2c2c, #5a5a5a, #8a8a8a)
- ✅ **Colori Interattivi**: Bottoni e controlli con palette grigia professionale Adobe Reader
- ✅ **Colori Accent**: Sostituiti blu con palette neutra Adobe Reader (#5a5a5a, #4a9eff)
- ✅ **Theme Manager**: Aggiornato per utilizzare ADOBE_READER_THEME invece di DARK_THEME

#### FASE 2: ICONE STILE ADOBE READER DC ✅
- ✅ **Icone Minimaliste**: Create icone con linee sottili (1-2px) stile Adobe Reader
- ✅ **Dimensioni Corrette**: 16x16px per toolbar, 14x14px per sidebar
- ✅ **Colore Monocromatico**: Tutte le icone utilizzano #5a5a5a (grigio Adobe Reader)
- ✅ **Design Geometrico**: Icone con design pulito e minimalista
- ✅ **Icon Manager**: Aggiornato per creare icone Adobe Reader style

#### FASE 3: LINEE SEPARATRICI SOTTILI ✅
- ✅ **Spessore 1px**: Implementate linee separatrici esatte Adobe Reader
- ✅ **Colore Esatto**: Utilizzato #d0d0d0 (colore separatori Adobe Reader)
- ✅ **Posizionamento**: Sotto toolbar, tra sidebar e viewer, sopra statusbar
- ✅ **Stile Continuo**: Linee continue senza tratteggio
- ✅ **Toolbar**: Aggiornata con separatori Adobe Reader tra gruppi di controlli

#### FASE 4: LAYOUT STRUTTURALE ADOBE READER DC ✅
- ✅ **Toolbar**: Altezza esatta 40px con padding e spacing Adobe Reader
- ✅ **Sidebar**: Larghezza esatta 240px con separatore verticale
- ✅ **Statusbar**: Altezza esatta 24px con informazioni allineate a destra
- ✅ **Viewer Area**: Background bianco puro con bordi corretti
- ✅ **Spacing**: Utilizzato spacing Adobe Reader per tutti i componenti

#### FASE 5: EFFETTI VISIVI SOTTILI ✅
- ✅ **Ombre Sottili**: Box-shadow molto leggere (rgba(0,0,0,0.08))
- ✅ **Bordi Sottili**: Bordi 1px con colore #d0d0d0
- ✅ **Corner Radius**: Utilizzato 3px (Adobe Reader style) invece di 6px
- ✅ **Hover Effects**: Transizioni smooth con colori Adobe Reader
- ✅ **Focus States**: Outline sottile con colore Adobe Reader

### 2025-06-17 - SEMPLIFICAZIONE DESIGN COMPLETATA ✅

#### Design Minimalista e Pulito
- ✅ **Palette Bottoni Neutra**: Cambiata da blu (#2f81f7) a grigio neutro per design più professionale
- ✅ **Cornici Rimosse**: Eliminati border_width e corner_radius eccessivi per layout più pulito
- ✅ **Spacing Ridotto**: Utilizzato spacing_sm invece di spacing_md per design più compatto
- ✅ **Layout Semplificato**: Rimossi frame secondari e styling eccessivo mantenendo funzionalità
- ✅ **Design Consistente**: Applicato stile minimalista a viewer, placeholder e loading frames

### 2025-06-17 - FASE 1 COMPLETATA: UI CORE MODERNIZZATO

#### FASE 1.1 - Viewer Area Premium ✅
- ✅ **Viewer Modernizzato**: Design premium con bordi eleganti, shadows e corner radius
- ✅ **Placeholder Moderno**: Nuovo design con icone Unicode e layout professionale
- ✅ **Loading States**: Indicatori di caricamento per file grandi con threading asincrono
- ✅ **Canvas Migliorato**: Background pattern subtile e shadow effects per PDF
- ✅ **Smooth Scrolling**: Animazioni fluide per lo scrolling del mouse (~60fps)
- ✅ **Styling Avanzato**: Integrazione completa con design system GitHub Dark

#### FASE 1.2 - Menu Contestuali Professionali ✅
- ✅ **Menu Modernizzati**: Icone Unicode moderne per tutti i menu items
- ✅ **GitHub Dark Theme**: Styling consistente con palette colori professionale
- ✅ **Hover Effects**: Transizioni fluide e feedback visuale
- ✅ **Recent Files**: Numerazione per accesso rapido e icone descrittive
- ✅ **Keyboard Shortcuts**: Visibili nei menu per migliore usabilità

#### FASE 1.3 - Layout Ottimizzato ✅
- ✅ **Spacing Consistente**: Sistema di spacing standardizzato dal theme manager
- ✅ **Grid Layout**: Layout responsive con grid system moderno
- ✅ **Responsive Design**: Auto-hide sidebar su schermi piccoli (<1000px)
- ✅ **Window Resizing**: Gestione intelligente del resize con minimum size
- ✅ **Border Styling**: Corner radius e border consistenti per tutti i componenti

### Modifiche Tecniche REPLICA ADOBE READER DC
- **File Modificati**:
  - `src/app/config.py` - Aggiunto ADOBE_READER_THEME con colori esatti
  - `src/ui/theme.py` - Aggiornato per utilizzare palette Adobe Reader
  - `src/ui/toolbar.py` - Ridisegnata con dimensioni e colori Adobe Reader esatti
  - `src/ui/statusbar.py` - Aggiornata con styling Adobe Reader completo
  - `src/app/main_window.py` - Layout con dimensioni strutturali Adobe Reader
  - `src/utils/icon_manager.py` - Icone ridisegnate in stile Adobe Reader
  - `src/pdf/viewer.py` - Fix threading per compatibilità
- **Nuovi Metodi Adobe Reader**:
  - `_draw_adobe_*_icon()` - Metodi per icone Adobe Reader style
  - `_configure_modern_theme()` - Configurazione tema Adobe Reader
  - Separatori sottili con `ctk.CTkFrame` 1px
- **Colori Adobe Reader**: Palette completa con colori esatti (#f8f8f8, #e5e5e5, #d0d0d0, #2c2c2c, #5a5a5a)
- **Dimensioni Adobe Reader**: Toolbar 40px, Sidebar 240px, Statusbar 24px

### Modifiche Tecniche FASE 1 (Precedenti)
- **File Modificati**:
  - `src/pdf/viewer.py` - Viewer premium con loading states
  - `src/ui/menubar.py` - Menu moderni con icone e GitHub Dark theme
  - `src/app/main_window.py` - Layout responsive e spacing ottimizzato
- **Nuovi Metodi**:
  - `_create_modern_placeholder()`, `_create_loading_indicator()`, `_smooth_scroll()`
  - `_get_modern_menu_style()`, `_on_window_resize()`
- **Threading**: Caricamento asincrono per file PDF grandi
- **Responsive**: Auto-layout basato su dimensioni finestra

## Prossimi Sviluppi

### REPLICA ADOBE READER DC - COMPLETATA ✅
- [x] **FASE 1**: Palette colori Adobe Reader DC esatta
- [x] **FASE 2**: Icone stile Adobe Reader con linee sottili
- [x] **FASE 3**: Linee separatrici sottili (1px, #d0d0d0)
- [x] **FASE 4**: Layout strutturale Adobe Reader (40px toolbar, 240px sidebar, 24px statusbar)
- [x] **FASE 5**: Effetti visivi sottili Adobe Reader

### Funzionalità Avanzate Future
- [ ] Search dialog Adobe Reader style
- [ ] Sistema thumbnails Adobe Reader
- [ ] Menu contestuali Adobe Reader style
- [ ] Annotazioni PDF
- [ ] Stampa documenti
- [ ] Esportazione immagini
- [ ] Plugin system
- [ ] Multi-tab support
- [ ] Modalità presentazione
- [ ] Segnalibri personalizzati

---
*Ultimo aggiornamento: 2025-06-17*
*Versione documento: 1.1*
