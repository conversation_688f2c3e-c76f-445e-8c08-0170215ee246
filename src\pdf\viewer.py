"""
PDF Viewer Component for ArchPDF - Modern PDF viewing widget with premium styling.
File: src/pdf/viewer.py
"""

import tkinter as tk
from tkinter import ttk
import customtkinter as ctk
from PIL import Image, ImageTk
import io
import threading
import time
from typing import Optional, Tuple
import logging

from app.config import MIN_ZOOM, MAX_ZOOM, ZOOM_STEP

logger = logging.getLogger(__name__)

class PDFViewer(ctk.CTkFrame):
    """Main PDF viewing component with zoom and navigation capabilities."""
    
    def __init__(self, parent, app):
        """
        Initialize the PDF viewer.
        
        Args:
            parent: Parent widget
            app: Main application instance
        """
        super().__init__(parent)
        self.app = app
        self.pdf_engine = app.pdf_engine
        
        # Viewer state
        self.zoom_level = 100  # Percentage
        self.fit_mode = "none"  # none, fit_width, fit_page
        self.current_image = None
        self.current_photo = None
        
        # Create UI
        self._create_ui()
        
        # Bind events
        self._bind_events()
    
    def _create_ui(self):
        """Create the modern viewer UI with premium styling."""
        # Configure grid
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)

        # Create clean scrollable frame with minimal styling
        scrollable_style = self.app.theme_manager.get_frame_style("primary")
        scrollable_style.update({
            "corner_radius": 0,
            "border_width": 0
        })

        self.scrollable_frame = ctk.CTkScrollableFrame(
            self,
            **scrollable_style
        )
        self.scrollable_frame.grid(
            row=0, column=0, sticky="nsew",
            padx=0,
            pady=0
        )

        # Configure scrollable frame
        self.scrollable_frame.grid_rowconfigure(0, weight=1)
        self.scrollable_frame.grid_columnconfigure(0, weight=1)

        # Create modern canvas for PDF display with gradient background
        canvas_bg = self.app.theme_manager.get_color("surface_secondary")
        self.canvas = tk.Canvas(
            self.scrollable_frame,
            bg=canvas_bg,
            highlightthickness=0,
            relief="flat",
            borderwidth=0
        )
        self.canvas.grid(row=0, column=0, sticky="nsew")

        # Create modern placeholder with icon and elegant styling
        self._create_modern_placeholder()

        # Create loading indicator (initially hidden)
        self._create_loading_indicator()

    def _create_modern_placeholder(self):
        """Create a modern, elegant placeholder for when no PDF is loaded."""
        # Main placeholder frame with clean, minimal styling
        placeholder_style = self.app.theme_manager.get_frame_style("primary")
        placeholder_style.update({
            "corner_radius": 0,
            "border_width": 0,
            "fg_color": self.app.theme_manager.get_color("surface_primary")
        })

        self.placeholder_frame = ctk.CTkFrame(
            self.scrollable_frame,
            **placeholder_style
        )

        # Configure placeholder frame grid
        self.placeholder_frame.grid_rowconfigure(0, weight=1)
        self.placeholder_frame.grid_rowconfigure(1, weight=0)
        self.placeholder_frame.grid_rowconfigure(2, weight=0)
        self.placeholder_frame.grid_rowconfigure(3, weight=1)
        self.placeholder_frame.grid_columnconfigure(0, weight=1)

        # Spacer top
        ctk.CTkLabel(self.placeholder_frame, text="").grid(row=0, column=0)

        # Modern PDF icon (using Unicode)
        icon_label = ctk.CTkLabel(
            self.placeholder_frame,
            text="📄",
            font=("Segoe UI", 48),
            text_color=self.app.theme_manager.get_color("text_tertiary")
        )
        icon_label.grid(row=1, column=0, pady=(0, self.app.theme_manager.get_spacing("lg")))

        # Main message
        main_label = ctk.CTkLabel(
            self.placeholder_frame,
            text="No PDF Document Loaded",
            **self.app.theme_manager.get_label_style("heading")
        )
        main_label.grid(row=2, column=0, pady=(0, self.app.theme_manager.get_spacing("sm")))

        # Instruction text
        instruction_label = ctk.CTkLabel(
            self.placeholder_frame,
            text="Use File → Open or drag & drop a PDF file to get started",
            **self.app.theme_manager.get_label_style("secondary")
        )
        instruction_label.grid(row=3, column=0, pady=(0, self.app.theme_manager.get_spacing("xl")))

        # Initially show placeholder with minimal padding
        self.placeholder_frame.grid(row=0, column=0, sticky="nsew",
                                   padx=self.app.theme_manager.get_spacing("xl"),
                                   pady=self.app.theme_manager.get_spacing("xl"))

    def _create_loading_indicator(self):
        """Create a modern loading indicator for large files."""
        # Loading frame with clean, minimal styling
        loading_style = self.app.theme_manager.get_frame_style("primary")
        loading_style.update({
            "corner_radius": 0,
            "border_width": 0,
            "fg_color": self.app.theme_manager.get_color("surface_primary")
        })

        self.loading_frame = ctk.CTkFrame(
            self.scrollable_frame,
            **loading_style
        )

        # Configure loading frame grid
        self.loading_frame.grid_rowconfigure(0, weight=1)
        self.loading_frame.grid_rowconfigure(1, weight=0)
        self.loading_frame.grid_rowconfigure(2, weight=0)
        self.loading_frame.grid_rowconfigure(3, weight=1)
        self.loading_frame.grid_columnconfigure(0, weight=1)

        # Spacer top
        ctk.CTkLabel(self.loading_frame, text="").grid(row=0, column=0)

        # Loading spinner (using Unicode)
        self.loading_icon = ctk.CTkLabel(
            self.loading_frame,
            text="⏳",
            font=("Segoe UI", 32),
            text_color=self.app.theme_manager.get_color("accent_primary")
        )
        self.loading_icon.grid(row=1, column=0, pady=(0, self.app.theme_manager.get_spacing("md")))

        # Loading message
        self.loading_label = ctk.CTkLabel(
            self.loading_frame,
            text="Loading PDF Document...",
            **self.app.theme_manager.get_label_style("primary")
        )
        self.loading_label.grid(row=2, column=0)

        # Progress info
        self.loading_info = ctk.CTkLabel(
            self.loading_frame,
            text="Please wait while the document is being processed",
            **self.app.theme_manager.get_label_style("secondary")
        )
        self.loading_info.grid(row=3, column=0, pady=(self.app.theme_manager.get_spacing("sm"), 0))

        # Initially hide loading indicator
        self.loading_frame.grid_remove()

    def _bind_events(self):
        """Bind mouse and keyboard events."""
        # Mouse wheel for zooming (with Ctrl)
        self.canvas.bind("<Control-MouseWheel>", self._on_ctrl_mousewheel)
        
        # Mouse wheel for scrolling
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        
        # Mouse drag for panning
        self.canvas.bind("<Button-1>", self._on_mouse_press)
        self.canvas.bind("<B1-Motion>", self._on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self._on_mouse_release)
        
        # Canvas resize
        self.canvas.bind("<Configure>", self._on_canvas_configure)
        
        # Focus for keyboard events
        self.canvas.bind("<Button-1>", lambda e: self.canvas.focus_set())
        
        # Variables for mouse dragging
        self.drag_start_x = 0
        self.drag_start_y = 0
        self.is_dragging = False
    
    def load_document(self):
        """Load the current document into the viewer with modern loading states."""
        if not self.pdf_engine.is_document_loaded():
            self.clear()
            return

        # Show loading indicator for large files
        self._show_loading_state()

        # Use threading for large file loading to prevent UI freeze
        def load_in_background():
            try:
                # Simulate processing time for large files (can be removed in production)
                time.sleep(0.1)  # Brief delay to show loading state

                # Load first page on main thread
                self.app.root.after(0, self._complete_document_load)

            except Exception as e:
                logger.error(f"Error loading document: {e}")
                self.app.root.after(0, self.clear)

        # Start background loading
        threading.Thread(target=load_in_background, daemon=True).start()

        logger.info("Document loading started")

    def _show_loading_state(self):
        """Show modern loading indicator."""
        # Hide placeholder and canvas
        self.placeholder_frame.grid_remove()
        self.canvas.grid_remove()

        # Show loading indicator with minimal padding
        self.loading_frame.grid(row=0, column=0, sticky="nsew",
                               padx=self.app.theme_manager.get_spacing("xl"),
                               pady=self.app.theme_manager.get_spacing("xl"))

    def _complete_document_load(self):
        """Complete the document loading process."""
        # Hide loading indicator
        self.loading_frame.grid_remove()

        # Show canvas
        self.canvas.grid(row=0, column=0, sticky="nsew")

        # Load first page
        self.update_page()

        logger.info("Document loaded in viewer")

    def clear(self):
        """Clear the viewer with modern state management."""
        # Clear canvas
        self.canvas.delete("all")
        self.current_image = None
        self.current_photo = None

        # Hide canvas and loading indicator
        self.canvas.grid_remove()
        self.loading_frame.grid_remove()

        # Show placeholder with minimal padding
        self.placeholder_frame.grid(row=0, column=0, sticky="nsew",
                                   padx=self.app.theme_manager.get_spacing("xl"),
                                   pady=self.app.theme_manager.get_spacing("xl"))

        logger.info("Viewer cleared")
    
    def update_page(self):
        """Update the displayed page."""
        if not self.pdf_engine.is_document_loaded():
            return
        
        current_page = self.pdf_engine.get_current_page()
        
        # Calculate zoom factor
        zoom_factor = self.zoom_level / 100.0
        
        # Handle fit modes
        if self.fit_mode in ["fit_width", "fit_page"]:
            zoom_factor = self._calculate_fit_zoom()
        
        # Get page image
        image_data = self.pdf_engine.get_page_image(current_page, zoom_factor)
        
        if image_data:
            self._display_image(image_data)
        
        logger.debug(f"Updated page {current_page + 1} with zoom {zoom_factor:.2f}")
    
    def _display_image(self, image_data: bytes):
        """Display image data on the canvas with modern styling and effects."""
        try:
            # Load image
            image = Image.open(io.BytesIO(image_data))
            self.current_image = image

            # Convert to PhotoImage
            self.current_photo = ImageTk.PhotoImage(image)

            # Clear canvas
            self.canvas.delete("all")

            # Calculate canvas size with modern padding
            padding = self.app.theme_manager.get_spacing("4xl")
            canvas_width = max(image.width + padding * 2, self.canvas.winfo_width())
            canvas_height = max(image.height + padding * 2, self.canvas.winfo_height())

            # Configure canvas scroll region
            self.canvas.configure(scrollregion=(0, 0, canvas_width, canvas_height))

            # Create subtle background pattern for better PDF visibility
            self._create_canvas_background(canvas_width, canvas_height)

            # Center image on canvas
            x = canvas_width // 2
            y = canvas_height // 2

            # Create shadow effect for the PDF page
            shadow_offset = 3
            shadow_color = self.app.theme_manager.get_color("shadow_medium")

            # Create shadow rectangle (simplified shadow effect)
            shadow_x1 = x - image.width // 2 + shadow_offset
            shadow_y1 = y - image.height // 2 + shadow_offset
            shadow_x2 = x + image.width // 2 + shadow_offset
            shadow_y2 = y + image.height // 2 + shadow_offset

            self.canvas.create_rectangle(
                shadow_x1, shadow_y1, shadow_x2, shadow_y2,
                fill="#000000", outline="", stipple="gray25"
            )

            # Display main image
            self.canvas.create_image(x, y, image=self.current_photo, anchor="center")

            # Update canvas size
            self.canvas.configure(width=canvas_width, height=canvas_height)

            logger.debug(f"Image displayed with modern styling: {image.width}x{image.height}")

        except Exception as e:
            logger.error(f"Failed to display image: {e}")

    def _create_canvas_background(self, width: int, height: int):
        """Create a subtle background pattern for better PDF visibility."""
        # Create a subtle grid pattern
        grid_color = self.app.theme_manager.get_color("border_secondary")
        grid_size = 20

        # Draw subtle grid lines (very light)
        for x in range(0, width, grid_size):
            self.canvas.create_line(x, 0, x, height, fill=grid_color, width=1, stipple="gray12")

        for y in range(0, height, grid_size):
            self.canvas.create_line(0, y, width, y, fill=grid_color, width=1, stipple="gray12")
    
    def _calculate_fit_zoom(self) -> float:
        """Calculate zoom factor for fit modes."""
        if not self.pdf_engine.is_document_loaded():
            return 1.0
        
        current_page = self.pdf_engine.get_current_page()
        page_size = self.pdf_engine.get_page_size(current_page)
        
        if not page_size:
            return 1.0
        
        page_width, page_height = page_size
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            return 1.0
        
        # Calculate zoom factors
        width_zoom = (canvas_width - 40) / page_width
        height_zoom = (canvas_height - 40) / page_height
        
        if self.fit_mode == "fit_width":
            zoom_factor = width_zoom
        elif self.fit_mode == "fit_page":
            zoom_factor = min(width_zoom, height_zoom)
        else:
            zoom_factor = 1.0
        
        # Clamp zoom factor
        zoom_factor = max(MIN_ZOOM / 100.0, min(MAX_ZOOM / 100.0, zoom_factor))
        
        return zoom_factor
    
    def zoom_in(self):
        """Zoom in."""
        if self.zoom_level < MAX_ZOOM:
            self.zoom_level = min(MAX_ZOOM, self.zoom_level + ZOOM_STEP)
            self.fit_mode = "none"
            self.update_page()
    
    def zoom_out(self):
        """Zoom out."""
        if self.zoom_level > MIN_ZOOM:
            self.zoom_level = max(MIN_ZOOM, self.zoom_level - ZOOM_STEP)
            self.fit_mode = "none"
            self.update_page()
    
    def zoom_actual_size(self):
        """Set zoom to actual size (100%)."""
        self.zoom_level = 100
        self.fit_mode = "none"
        self.update_page()
    
    def zoom_fit_width(self):
        """Fit page width to window."""
        self.fit_mode = "fit_width"
        self.update_page()
        # Update zoom level for display
        self.zoom_level = int(self._calculate_fit_zoom() * 100)
    
    def zoom_fit_page(self):
        """Fit entire page to window."""
        self.fit_mode = "fit_page"
        self.update_page()
        # Update zoom level for display
        self.zoom_level = int(self._calculate_fit_zoom() * 100)
    
    def set_zoom(self, zoom_level: int):
        """Set specific zoom level."""
        self.zoom_level = max(MIN_ZOOM, min(MAX_ZOOM, zoom_level))
        self.fit_mode = "none"
        self.update_page()
    
    def get_zoom_level(self) -> int:
        """Get current zoom level."""
        return self.zoom_level
    
    def _on_ctrl_mousewheel(self, event):
        """Handle Ctrl+mouse wheel for zooming."""
        if event.delta > 0:
            self.zoom_in()
        else:
            self.zoom_out()
    
    def _on_mousewheel(self, event):
        """Handle mouse wheel for smooth scrolling."""
        # Calculate smooth scroll amount
        scroll_amount = int(-1 * (event.delta / 120))

        # Implement smooth scrolling with multiple smaller steps
        self._smooth_scroll(scroll_amount)

    def _smooth_scroll(self, scroll_amount: int):
        """Implement smooth scrolling animation."""
        if scroll_amount == 0:
            return

        # Break large scroll amounts into smaller steps for smoothness
        steps = min(abs(scroll_amount), 3)  # Max 3 steps for smoothness
        step_size = scroll_amount / steps if steps > 0 else scroll_amount

        def scroll_step(remaining_steps: int, current_step_size: float):
            if remaining_steps <= 0:
                return

            # Perform one scroll step
            self.canvas.yview_scroll(int(current_step_size), "units")

            # Schedule next step with slight delay for smooth animation
            if remaining_steps > 1:
                self.after(16, lambda: scroll_step(remaining_steps - 1, current_step_size))  # ~60fps

        # Start smooth scrolling
        scroll_step(steps, step_size)
    
    def _on_mouse_press(self, event):
        """Handle mouse press for dragging."""
        self.drag_start_x = event.x
        self.drag_start_y = event.y
        self.is_dragging = False
        self.canvas.configure(cursor="hand2")
    
    def _on_mouse_drag(self, event):
        """Handle mouse drag for panning."""
        if not self.is_dragging:
            self.is_dragging = True
        
        # Calculate drag distance
        dx = event.x - self.drag_start_x
        dy = event.y - self.drag_start_y
        
        # Pan the view
        self.canvas.xview_scroll(-dx, "units")
        self.canvas.yview_scroll(-dy, "units")
        
        # Update drag start position
        self.drag_start_x = event.x
        self.drag_start_y = event.y
    
    def _on_mouse_release(self, event):
        """Handle mouse release."""
        self.is_dragging = False
        self.canvas.configure(cursor="")
    
    def _on_canvas_configure(self, event):
        """Handle canvas resize."""
        # Update page if in fit mode
        if self.fit_mode in ["fit_width", "fit_page"]:
            self.update_page()
