"""
Main application window for ArchPDF.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import customtkinter as ctk
from pathlib import Path
import logging
from typing import Optional

from app.config import APP_NAME, APP_VERSION, DARK_THEME, DEFAULT_SETTINGS
from pdf.engine import PDFEngine
from pdf.search import PDFSearchEngine
from ui.menubar import MenuBar
from ui.toolbar import ToolBar
from ui.statusbar import StatusBar
from ui.sidebar import SideBar
from ui.theme import ThemeManager
from ui.search_dialog import SearchDialog
from pdf.viewer import PDFViewer
from utils.settings_manager import SettingsManager
from utils.file_manager import FileManager
from utils.icon_manager import IconManager
from utils.error_handler import ErrorHandler, error_handler

logger = logging.getLogger(__name__)

class ArchPDFApp:
    """Main application class for ArchPDF."""
    
    def __init__(self):
        """Initialize the ArchPDF application."""
        # Initialize error handler first
        self.error_handler = ErrorHandler(self)

        # Set up global exception handler
        import sys
        sys.excepthook = self.error_handler.handle_exception

        # Set up logging
        logging.basicConfig(level=logging.INFO)

        # Initialize core components
        self.pdf_engine = PDFEngine()
        self.search_engine = PDFSearchEngine(self.pdf_engine)
        self.settings_manager = SettingsManager()
        self.file_manager = FileManager()
        
        # Load settings
        self.settings = self.settings_manager.load_settings()
        
        # Set up CustomTkinter with Adobe Reader style
        ctk.set_appearance_mode("light")  # Adobe Reader uses light theme
        ctk.set_default_color_theme("green")  # Will be overridden by theme manager
        
        # Create main window
        self.root = ctk.CTk()
        self.root.title(f"{APP_NAME} v{APP_VERSION}")
        self.root.geometry(f"{self.settings['window_width']}x{self.settings['window_height']}")
        
        # Set window icon (if available)
        try:
            icon_path = Path("assets/icons/app_icon.ico")
            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
        except Exception:
            pass  # Icon not available, continue without it
        
        # Initialize theme manager
        self.theme_manager = ThemeManager(self.root)

        # Initialize icon manager
        self.icon_manager = IconManager(self.theme_manager)
        
        # Current file
        self.current_file: Optional[Path] = None
        
        # Create UI components
        self._create_ui()
        
        # Set up event handlers
        self._setup_event_handlers()
        
        # Apply modern theme
        self.theme_manager.apply_modern_theme()

        # DEBUG: Verifica stato tema CustomTkinter
        self._debug_customtkinter_theme()

        # FORCE Adobe Reader colors after everything is created
        self._force_adobe_reader_colors()

        # DEBUG: Verifica colori applicati
        self._debug_applied_colors()

        logger.info("ArchPDF application initialized")
    
    def _create_ui(self):
        """Create the modern user interface with optimized layout and spacing."""
        # Get consistent spacing from theme manager
        spacing_sm = self.theme_manager.get_spacing("sm")
        spacing_md = self.theme_manager.get_spacing("md")
        spacing_lg = self.theme_manager.get_spacing("lg")

        # Create main container with Adobe Reader DC exact styling
        self.main_container = ctk.CTkFrame(
            self.root,
            fg_color="#f8f8f8",      # Adobe Reader exact main background
            corner_radius=0,         # Adobe Reader uses no corner radius
            border_width=0           # No border
        )
        self.main_container.pack(fill="both", expand=True, padx=0, pady=0)

        # Configure main container grid for better layout control
        self.main_container.grid_rowconfigure(1, weight=1)  # Content area expands
        self.main_container.grid_columnconfigure(0, weight=1)

        # Create menu bar
        self.menubar = MenuBar(self.root, self)

        # Create toolbar with Adobe Reader DC exact dimensions (40px height)
        self.toolbar = ToolBar(self.main_container, self)
        self.toolbar.configure(height=40)  # Adobe Reader exact toolbar height
        self.toolbar.grid(row=0, column=0, sticky="ew", padx=0, pady=0)

        # Add Adobe Reader style separator line under toolbar
        toolbar_separator = ctk.CTkFrame(
            self.main_container,
            height=1,
            fg_color="#d0d0d0",  # Adobe Reader exact separator color
            corner_radius=0
        )
        toolbar_separator.grid(row=0, column=0, sticky="ew", padx=0, pady=(39, 0))

        # Create main content area with Adobe Reader DC exact styling
        self.content_frame = ctk.CTkFrame(
            self.main_container,
            fg_color="#f8f8f8",      # Adobe Reader exact content background
            corner_radius=0,         # Adobe Reader uses no corner radius
            border_width=0           # No border
        )
        self.content_frame.grid(row=1, column=0, sticky="nsew",
                               padx=0, pady=0)

        # Configure content frame for responsive layout
        self.content_frame.grid_rowconfigure(0, weight=1)
        self.content_frame.grid_columnconfigure(0, weight=1)

        # Create paned window for sidebar and viewer with Adobe Reader DC exact styling
        self.paned_window = ctk.CTkFrame(
            self.content_frame,
            fg_color="#f8f8f8",      # Adobe Reader exact paned background
            corner_radius=0,         # Adobe Reader uses no corner radius
            border_width=0           # No border
        )
        self.paned_window.grid(row=0, column=0, sticky="nsew",
                              padx=0, pady=0)

        # Configure responsive grid for paned layout
        self.paned_window.grid_columnconfigure(1, weight=1)  # Viewer expands
        self.paned_window.grid_rowconfigure(0, weight=1)

        # Create sidebar with Adobe Reader DC exact width (240px)
        self.sidebar = SideBar(self.paned_window, self)
        self.sidebar.configure(width=240)  # Adobe Reader exact sidebar width
        if self.settings['show_sidebar']:
            self.sidebar.grid(row=0, column=0, sticky="nsew", padx=0, pady=0)

        # Add Adobe Reader style separator line between sidebar and viewer
        if self.settings['show_sidebar']:
            sidebar_separator = ctk.CTkFrame(
                self.paned_window,
                width=1,
                fg_color="#d0d0d0",  # Adobe Reader exact separator color
                corner_radius=0
            )
            sidebar_separator.grid(row=0, column=0, sticky="nse", padx=(239, 0), pady=0)

        # Create PDF viewer with clean design
        self.pdf_viewer = PDFViewer(self.paned_window, self)
        self.pdf_viewer.grid(row=0, column=1, sticky="nsew")

        # Add Adobe Reader style separator line above statusbar
        statusbar_separator = ctk.CTkFrame(
            self.main_container,
            height=1,
            fg_color="#d0d0d0",  # Adobe Reader exact separator color
            corner_radius=0
        )
        statusbar_separator.grid(row=2, column=0, sticky="ew", padx=0, pady=0)

        # Create status bar with Adobe Reader DC exact height (24px)
        self.statusbar = StatusBar(self.main_container, self)
        self.statusbar.configure(height=24)  # Adobe Reader exact statusbar height
        self.statusbar.grid(row=3, column=0, sticky="ew", padx=0, pady=0)

        # Set minimum window size for responsive behavior
        self.root.minsize(800, 600)

        # Bind resize events for responsive layout
        self.root.bind("<Configure>", self._on_window_resize)

        # Update UI state
        self._update_ui_state()
    
    def _setup_event_handlers(self):
        """Set up event handlers."""
        # Window events
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Keyboard shortcuts
        self.root.bind("<Control-o>", lambda e: self.open_file())
        self.root.bind("<Control-q>", lambda e: self.on_closing())
        self.root.bind("<Control-f>", lambda e: self.show_search_dialog())
        self.root.bind("<Control-plus>", lambda e: self.zoom_in())
        self.root.bind("<Control-minus>", lambda e: self.zoom_out())
        self.root.bind("<Control-0>", lambda e: self.zoom_actual_size())
        self.root.bind("<F11>", lambda e: self.toggle_fullscreen())
        self.root.bind("<Prior>", lambda e: self.previous_page())  # Page Up
        self.root.bind("<Next>", lambda e: self.next_page())       # Page Down
        
        # Focus events
        self.root.bind("<FocusIn>", self._on_focus_in)
    
    def _update_ui_state(self):
        """Update UI state based on current document."""
        has_document = self.pdf_engine.is_document_loaded()
        
        # Update toolbar state
        if hasattr(self, 'toolbar'):
            self.toolbar.update_state(has_document)
        
        # Update menu state
        if hasattr(self, 'menubar'):
            self.menubar.update_state(has_document)
        
        # Update status bar
        if hasattr(self, 'statusbar'):
            if has_document:
                current_page = self.pdf_engine.get_current_page() + 1
                total_pages = self.pdf_engine.page_count
                self.statusbar.update_page_info(current_page, total_pages)
            else:
                self.statusbar.update_page_info(0, 0)
    
    @error_handler("opening file")
    def open_file(self, file_path: Optional[str] = None):
        """Open a PDF file."""
        try:
            if not file_path:
                file_path = filedialog.askopenfilename(
                    title="Open PDF File",
                    filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                    initialdir=str(Path.home())
                )

            if file_path:
                # Validate file
                if not self.file_manager.is_valid_pdf_file(file_path):
                    self.error_handler.show_error_dialog(
                        "Invalid File",
                        f"The selected file is not a valid PDF document.\n\nFile: {file_path}",
                        "warning"
                    )
                    return

                # Show progress for large files
                file_info = self.file_manager.get_file_info(file_path)
                if file_info and file_info['size'] > 10 * 1024 * 1024:  # 10MB
                    progress = self.error_handler.show_progress_dialog(
                        "Loading PDF", f"Loading {Path(file_path).name}...", self.root
                    )
                    progress.update_progress(0.3, "Opening document...")

                if self.pdf_engine.load_document(file_path):
                    self.current_file = Path(file_path)
                    self.file_manager.add_recent_file(file_path)

                    if 'progress' in locals():
                        progress.update_progress(0.6, "Loading viewer...")

                    # Update window title
                    self.root.title(f"{APP_NAME} - {self.current_file.name}")

                    # Update viewer
                    self.pdf_viewer.load_document()

                    if 'progress' in locals():
                        progress.update_progress(0.8, "Loading sidebar...")

                    # Update sidebar
                    self.sidebar.load_document()

                    # Update UI state
                    self._update_ui_state()

                    if 'progress' in locals():
                        progress.update_progress(1.0, "Complete")
                        progress.destroy()

                    # Update status
                    self.statusbar.set_status(f"Loaded: {self.current_file.name}")

                    logger.info(f"Opened file: {file_path}")
                else:
                    if 'progress' in locals():
                        progress.destroy()
                    self.error_handler.handle_pdf_error(
                        Exception("Failed to load PDF document"), file_path
                    )

        except Exception as e:
            if 'progress' in locals():
                progress.destroy()
            self.error_handler.handle_pdf_error(e, file_path or "")
    
    def close_file(self):
        """Close the current file."""
        if self.pdf_engine.is_document_loaded():
            self.pdf_engine.close_document()
            self.current_file = None
            
            # Update window title
            self.root.title(f"{APP_NAME} v{APP_VERSION}")
            
            # Clear viewer
            self.pdf_viewer.clear()
            
            # Clear sidebar
            self.sidebar.clear()
            
            # Update UI state
            self._update_ui_state()
            
            logger.info("Closed current file")
    
    def next_page(self):
        """Go to next page."""
        if self.pdf_engine.next_page():
            self.pdf_viewer.update_page()
            self._update_ui_state()
    
    def previous_page(self):
        """Go to previous page."""
        if self.pdf_engine.previous_page():
            self.pdf_viewer.update_page()
            self._update_ui_state()
    
    def go_to_page(self, page_num: int):
        """Go to specific page."""
        if self.pdf_engine.set_current_page(page_num - 1):  # Convert to 0-based
            self.pdf_viewer.update_page()
            self._update_ui_state()
    
    def zoom_in(self):
        """Zoom in."""
        self.pdf_viewer.zoom_in()
        self._update_ui_state()
    
    def zoom_out(self):
        """Zoom out."""
        self.pdf_viewer.zoom_out()
        self._update_ui_state()
    
    def zoom_actual_size(self):
        """Set zoom to actual size."""
        self.pdf_viewer.zoom_actual_size()
        self._update_ui_state()
    
    def zoom_fit_width(self):
        """Fit page width to window."""
        self.pdf_viewer.zoom_fit_width()
        self._update_ui_state()
    
    def zoom_fit_page(self):
        """Fit entire page to window."""
        self.pdf_viewer.zoom_fit_page()
        self._update_ui_state()
    
    def show_search_dialog(self):
        """Show search dialog."""
        if self.pdf_engine.is_document_loaded():
            SearchDialog(self.root, self)
        else:
            messagebox.showinfo("Search", "Please open a PDF document first.")
    
    def toggle_sidebar(self):
        """Toggle sidebar visibility with minimal spacing."""
        spacing_sm = self.theme_manager.get_spacing("sm")

        if self.settings['show_sidebar']:
            self.sidebar.grid_remove()
            self.settings['show_sidebar'] = False
        else:
            self.sidebar.grid(row=0, column=0, sticky="nsew",
                             padx=(0, spacing_sm))
            self.settings['show_sidebar'] = True

        self.settings_manager.save_settings(self.settings)

    def _on_window_resize(self, event):
        """Handle window resize events for responsive layout."""
        # Only handle resize events for the main window
        if event.widget != self.root:
            return

        # Get current window size
        width = self.root.winfo_width()
        height = self.root.winfo_height()

        # Responsive sidebar behavior
        if width < 1000 and self.settings['show_sidebar']:
            # Auto-hide sidebar on small screens
            self.sidebar.grid_remove()
        elif width >= 1000 and self.settings['show_sidebar']:
            # Show sidebar on larger screens
            spacing_sm = self.theme_manager.get_spacing("sm")
            self.sidebar.grid(row=0, column=0, sticky="nsew",
                             padx=(0, spacing_sm))

        # Update layout if needed
        self.root.update_idletasks()

    def _force_adobe_reader_colors(self):
        """Force Adobe Reader DC exact colors using direct widget manipulation."""
        try:
            import customtkinter as ctk

            # RADICAL APPROACH: Directly modify widget internal colors
            logger.info("Forcing Adobe Reader colors with direct manipulation...")

            # Force root window
            self.root.configure(fg_color="#f8f8f8")

            # Force main containers - use after_idle to ensure they're created
            self.root.after_idle(self._apply_colors_after_idle)

            logger.info("Adobe Reader color forcing initiated")

        except Exception as e:
            logger.warning(f"Could not force Adobe Reader colors: {e}")

    def _apply_colors_after_idle(self):
        """Apply Adobe Reader colors after all widgets are fully initialized."""
        try:
            # Force main container colors
            self.main_container.configure(fg_color="#f8f8f8")
            self.content_frame.configure(fg_color="#f8f8f8")
            self.paned_window.configure(fg_color="#f8f8f8")

            # Force toolbar colors
            if hasattr(self, 'toolbar'):
                self.toolbar.configure(fg_color="#e5e5e5")
                # Also force all toolbar children
                self._force_widget_colors(self.toolbar, "#e5e5e5")

            # Force sidebar colors
            if hasattr(self, 'sidebar'):
                self.sidebar.configure(fg_color="#f0f0f0")
                # Also force all sidebar children
                self._force_widget_colors(self.sidebar, "#f0f0f0")

            # Force statusbar colors
            if hasattr(self, 'statusbar'):
                self.statusbar.configure(fg_color="#e5e5e5")
                # Also force all statusbar children
                self._force_widget_colors(self.statusbar, "#e5e5e5")

            # Force update
            self.root.update_idletasks()

            # Schedule another round to be absolutely sure
            self.root.after(200, self._final_color_force)

            logger.info("Colors applied after idle")

        except Exception as e:
            logger.warning(f"Could not apply colors after idle: {e}")

    def _force_widget_colors(self, widget, color):
        """Recursively force colors on a widget and all its children."""
        try:
            # Force color on the widget itself
            if hasattr(widget, 'configure'):
                widget.configure(fg_color=color)

            # Force colors on all children
            for child in widget.winfo_children():
                if hasattr(child, 'configure'):
                    try:
                        child.configure(fg_color=color)
                    except:
                        pass  # Some widgets might not support fg_color

                # Recursively apply to children's children
                self._force_widget_colors(child, color)

        except Exception as e:
            pass  # Ignore errors for individual widgets

    def _final_color_force(self):
        """Final aggressive color forcing."""
        try:
            # One more round of color forcing
            self.main_container.configure(fg_color="#f8f8f8")

            if hasattr(self, 'toolbar'):
                self.toolbar.configure(fg_color="#e5e5e5")

            if hasattr(self, 'sidebar'):
                self.sidebar.configure(fg_color="#f0f0f0")

            if hasattr(self, 'statusbar'):
                self.statusbar.configure(fg_color="#e5e5e5")

            self.root.update()
            logger.info("Final color force completed")

        except Exception as e:
            logger.warning(f"Final color force failed: {e}")

    def _delayed_color_refresh(self):
        """Delayed color refresh to ensure visual update."""
        try:
            # Force another round of color application
            self.main_container.configure(fg_color="#f8f8f8")
            if hasattr(self, 'toolbar'):
                self.toolbar.configure(fg_color="#e5e5e5")
            if hasattr(self, 'sidebar'):
                self.sidebar.configure(fg_color="#f0f0f0")
            if hasattr(self, 'statusbar'):
                self.statusbar.configure(fg_color="#e5e5e5")

            # Force complete redraw
            self.root.update()
            logger.info("Delayed color refresh completed")
        except Exception as e:
            logger.warning(f"Delayed color refresh failed: {e}")

    def _debug_customtkinter_theme(self):
        """Debug CustomTkinter theme state."""
        import customtkinter as ctk
        logger.info("=== DEBUG CUSTOMTKINTER THEME ===")
        logger.info(f"Appearance mode: {ctk.get_appearance_mode()}")
        logger.info(f"Color theme: {ctk.ThemeManager.theme}")
        logger.info(f"CTk fg_color: {ctk.ThemeManager.theme.get('CTk', {}).get('fg_color', 'NOT_SET')}")
        logger.info(f"CTkFrame fg_color: {ctk.ThemeManager.theme.get('CTkFrame', {}).get('fg_color', 'NOT_SET')}")
        logger.info("=== END DEBUG ===")

    def _debug_applied_colors(self):
        """Debug applied colors on components."""
        logger.info("=== DEBUG APPLIED COLORS ===")
        try:
            logger.info(f"Root fg_color: {self.root.cget('fg_color')}")
            logger.info(f"Main container fg_color: {self.main_container.cget('fg_color')}")
            if hasattr(self, 'toolbar'):
                logger.info(f"Toolbar fg_color: {self.toolbar.cget('fg_color')}")
            if hasattr(self, 'sidebar'):
                logger.info(f"Sidebar fg_color: {self.sidebar.cget('fg_color')}")
            if hasattr(self, 'statusbar'):
                logger.info(f"Statusbar fg_color: {self.statusbar.cget('fg_color')}")
        except Exception as e:
            logger.warning(f"Could not debug colors: {e}")
        logger.info("=== END DEBUG ===")

    def toggle_fullscreen(self):
        """Toggle fullscreen mode."""
        current_state = self.root.attributes('-fullscreen')
        self.root.attributes('-fullscreen', not current_state)
    
    def show_about(self):
        """Show about dialog."""
        about_text = f"""
{APP_NAME} v{APP_VERSION}

A professional PDF reader for Windows with modern dark theme.

Features:
• High-quality PDF rendering
• Dark theme with bicolor icons
• Search functionality
• Bookmarks navigation
• Zoom controls
• Recent files

Created by Augment Agent
        """
        messagebox.showinfo("About ArchPDF", about_text.strip())
    
    def _on_focus_in(self, event):
        """Handle focus in event."""
        # Check if file was modified externally
        if self.current_file and self.current_file.exists():
            # Could implement file change detection here
            pass
    
    def on_closing(self):
        """Handle application closing."""
        # Save settings
        self.settings['window_width'] = self.root.winfo_width()
        self.settings['window_height'] = self.root.winfo_height()
        self.settings_manager.save_settings(self.settings)
        
        # Close document
        self.close_file()
        
        # Destroy window
        self.root.destroy()
        
        logger.info("Application closed")
    
    def run(self):
        """Run the application."""
        logger.info("Starting ArchPDF application")
        self.root.mainloop()
