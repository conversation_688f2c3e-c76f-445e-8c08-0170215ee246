"""
Configuration settings for ArchPDF application.
"""

import os
from pathlib import Path

# Application Information
APP_NAME = "ArchPDF"
APP_VERSION = "1.0.0"
APP_AUTHOR = "Augment Agent"
APP_DESCRIPTION = "Professional PDF Reader for Windows"

# Paths
APP_DIR = Path(__file__).parent.parent.parent
ASSETS_DIR = APP_DIR / "assets"
ICONS_DIR = ASSETS_DIR / "icons"
THEMES_DIR = ASSETS_DIR / "themes"
CONFIG_DIR = Path.home() / ".archpdf"

# Ensure directories exist
CONFIG_DIR.mkdir(exist_ok=True)

# Default Settings
DEFAULT_SETTINGS = {
    "theme": "dark",
    "zoom_level": 100,
    "fit_mode": "fit_width",
    "recent_files_limit": 10,
    "window_width": 1200,
    "window_height": 800,
    "sidebar_width": 250,
    "show_sidebar": True,
    "show_toolbar": True,
    "show_statusbar": True,
    "auto_save_settings": True,
}

# Adobe Reader DC Professional Color Palette - Exact Replica
ADOBE_READER_THEME = {
    # === BACKGROUNDS - ADOBE READER DC EXACT COLORS ===
    "bg_primary": "#f8f8f8",        # Main background (very light gray)
    "bg_secondary": "#f0f0f0",      # Secondary background (light gray)
    "bg_tertiary": "#e8e8e8",       # Tertiary background (medium light gray)
    "bg_quaternary": "#ffffff",     # Input fields, pure white
    "bg_elevated": "#ffffff",       # Elevated surfaces (modals, dropdowns)

    # === SURFACES - ADOBE READER STRUCTURE ===
    "surface_primary": "#f8f8f8",     # Primary surface - main background
    "surface_secondary": "#e5e5e5",   # Secondary surface - toolbar background
    "surface_tertiary": "#f0f0f0",    # Tertiary surface - panels
    "surface_hover": "#f0f0f0",       # Hover state - light gray
    "surface_pressed": "#e0e0e0",     # Pressed state - medium gray

    # === TEXT COLORS - ADOBE READER TYPOGRAPHY ===
    "text_primary": "#2c2c2c",       # Primary text (dark gray, high contrast)
    "text_secondary": "#5a5a5a",     # Secondary text (medium gray)
    "text_tertiary": "#8a8a8a",      # Tertiary text (light gray)
    "text_placeholder": "#a0a0a0",   # Placeholder text
    "text_disabled": "#c0c0c0",      # Disabled text
    "text_inverse": "#ffffff",       # Text on dark backgrounds

    # === ACCENT COLORS - MINIMAL ADOBE STYLE ===
    "accent_primary": "#5a5a5a",      # Primary accent (neutral gray)
    "accent_secondary": "#8a8a8a",    # Secondary accent (lighter gray)
    "accent_success": "#4a9eff",      # Success blue (Adobe style)
    "accent_warning": "#ff9500",      # Warning orange
    "accent_error": "#ff3b30",        # Error red
    "accent_info": "#007aff",         # Info blue
    "accent_purple": "#af52de",       # Purple accent
    "accent_pink": "#ff2d92",         # Pink accent

    # === INTERACTIVE STATES - ADOBE READER BUTTONS ===
    "interactive_primary": "#5a5a5a",       # Primary buttons - Adobe gray
    "interactive_primary_hover": "#707070", # Primary hover - lighter gray
    "interactive_primary_pressed": "#404040", # Primary pressed - darker gray
    "interactive_secondary": "#f0f0f0",     # Secondary buttons - light gray
    "interactive_secondary_hover": "#e8e8e8", # Secondary hover
    "interactive_secondary_pressed": "#d8d8d8", # Secondary pressed

    # === BORDERS - ADOBE READER SEPARATORS ===
    "border_primary": "#d0d0d0",      # Primary borders - thin gray lines
    "border_secondary": "#e0e0e0",    # Secondary borders - lighter
    "border_tertiary": "#c0c0c0",     # Tertiary borders - darker
    "border_focus": "#5a5a5a",        # Focus borders - Adobe gray
    "border_error": "#ff3b30",        # Error borders
    "border_success": "#4a9eff",      # Success borders

    # === SHADOWS - ADOBE READER SUBTLE EFFECTS ===
    "shadow_small": "rgba(0, 0, 0, 0.08)",     # Very subtle shadows
    "shadow_medium": "rgba(0, 0, 0, 0.12)",    # Medium shadows
    "shadow_large": "rgba(0, 0, 0, 0.16)",     # Large shadows
    "shadow_focus": "rgba(90, 90, 90, 0.3)",   # Focus shadows - Adobe gray

    # === OVERLAYS - LIGHT THEME APPROPRIATE ===
    "overlay_light": "rgba(0, 0, 0, 0.02)",      # Very light overlay
    "overlay_medium": "rgba(0, 0, 0, 0.05)",     # Medium overlay
    "overlay_dark": "rgba(0, 0, 0, 0.3)",        # Dark overlay
    "overlay_backdrop": "rgba(0, 0, 0, 0.4)",    # Modal backdrop

    # === GRADIENTS - ADOBE READER STYLE ===
    "gradient_primary": "linear-gradient(135deg, #f8f8f8 0%, #f0f0f0 100%)",
    "gradient_secondary": "linear-gradient(135deg, #e5e5e5 0%, #d0d0d0 100%)",
    "gradient_success": "linear-gradient(135deg, #4a9eff 0%, #007aff 100%)",
    "gradient_error": "linear-gradient(135deg, #ff3b30 0%, #d70015 100%)",

    # === LEGACY COMPATIBILITY - UPDATED FOR ADOBE READER ===
    "fg_primary": "#2c2c2c",        # Alias for text_primary
    "fg_secondary": "#5a5a5a",      # Alias for text_secondary
    "fg_tertiary": "#8a8a8a",       # Alias for text_tertiary
    "border": "#d0d0d0",            # Alias for border_primary
    "hover": "#f0f0f0",             # Alias for surface_hover
    "selected": "#e0e0e0",          # Alias for interactive_primary_pressed
    "error": "#ff3b30",             # Alias for accent_error
    "warning": "#ff9500",           # Alias for accent_warning
    "success": "#4a9eff",           # Alias for accent_success
}

# Alias for backward compatibility
DARK_THEME = ADOBE_READER_THEME

# === TYPOGRAPHY SYSTEM ===
TYPOGRAPHY = {
    # Font Families (fallback chain)
    "font_primary": ("Segoe UI", "SF Pro Display", "Inter", "system-ui", "sans-serif"),
    "font_secondary": ("Segoe UI", "SF Pro Text", "Inter", "system-ui", "sans-serif"),
    "font_mono": ("Cascadia Code", "SF Mono", "Consolas", "Monaco", "monospace"),

    # Font Sizes (in points)
    "size_xs": 10,      # Extra small text
    "size_sm": 11,      # Small text
    "size_base": 12,    # Base text size
    "size_md": 13,      # Medium text
    "size_lg": 14,      # Large text
    "size_xl": 16,      # Extra large text
    "size_2xl": 18,     # 2X large text
    "size_3xl": 20,     # 3X large text
    "size_4xl": 24,     # 4X large text

    # Font Weights
    "weight_light": "normal",
    "weight_normal": "normal",
    "weight_medium": "bold",
    "weight_semibold": "bold",
    "weight_bold": "bold",

    # Line Heights (multipliers)
    "leading_tight": 1.2,
    "leading_normal": 1.4,
    "leading_relaxed": 1.6,
    "leading_loose": 1.8,
}

# === SPACING SYSTEM ===
SPACING = {
    "xs": 2,    # 2px
    "sm": 4,    # 4px
    "md": 8,    # 8px
    "lg": 12,   # 12px
    "xl": 16,   # 16px
    "2xl": 20,  # 20px
    "3xl": 24,  # 24px
    "4xl": 32,  # 32px
    "5xl": 40,  # 40px
    "6xl": 48,  # 48px
}

# === BORDER RADIUS ===
RADIUS = {
    "none": 0,
    "sm": 3,
    "md": 6,
    "lg": 8,
    "xl": 12,
    "2xl": 16,
    "full": 9999,
}

# === COMPONENT SIZES ===
COMPONENT_SIZES = {
    "button_height_sm": 28,
    "button_height_md": 32,
    "button_height_lg": 36,
    "button_height_xl": 40,

    "input_height_sm": 28,
    "input_height_md": 32,
    "input_height_lg": 36,

    "toolbar_height": 44,
    "statusbar_height": 28,
    "sidebar_width": 280,
    "sidebar_min_width": 200,
    "sidebar_max_width": 400,
}

# === ANIMATION SETTINGS ===
ANIMATIONS = {
    "duration_fast": 150,      # 150ms
    "duration_normal": 250,    # 250ms
    "duration_slow": 350,      # 350ms
    "easing": "ease-out",
}

# File Extensions
SUPPORTED_EXTENSIONS = [".pdf"]

# Zoom Settings
MIN_ZOOM = 25
MAX_ZOOM = 500
ZOOM_STEP = 25

# Recent Files
MAX_RECENT_FILES = 10
