"""
Configuration settings for ArchPDF application.
"""

import os
from pathlib import Path

# Application Information
APP_NAME = "ArchPDF"
APP_VERSION = "1.0.0"
APP_AUTHOR = "Augment Agent"
APP_DESCRIPTION = "Professional PDF Reader for Windows"

# Paths
APP_DIR = Path(__file__).parent.parent.parent
ASSETS_DIR = APP_DIR / "assets"
ICONS_DIR = ASSETS_DIR / "icons"
THEMES_DIR = ASSETS_DIR / "themes"
CONFIG_DIR = Path.home() / ".archpdf"

# Ensure directories exist
CONFIG_DIR.mkdir(exist_ok=True)

# Default Settings
DEFAULT_SETTINGS = {
    "theme": "dark",
    "zoom_level": 100,
    "fit_mode": "fit_width",
    "recent_files_limit": 10,
    "window_width": 1200,
    "window_height": 800,
    "sidebar_width": 250,
    "show_sidebar": True,
    "show_toolbar": True,
    "show_statusbar": True,
    "auto_save_settings": True,
}

# Modern Professional Color Palette - Inspired by VS Code, Figma, Adobe
DARK_THEME = {
    # === BACKGROUNDS ===
    "bg_primary": "#0d1117",      # Main background (GitHub dark)
    "bg_secondary": "#161b22",    # Secondary background (cards, panels)
    "bg_tertiary": "#21262d",     # Tertiary background (elevated elements)
    "bg_quaternary": "#30363d",   # Input fields, code blocks
    "bg_elevated": "#1c2128",     # Elevated surfaces (modals, dropdowns)

    # === SURFACES ===
    "surface_primary": "#161b22",   # Primary surface
    "surface_secondary": "#21262d", # Secondary surface
    "surface_tertiary": "#30363d",  # Tertiary surface
    "surface_hover": "#262c36",     # Hover state for surfaces
    "surface_pressed": "#2d333b",   # Pressed state for surfaces

    # === TEXT COLORS ===
    "text_primary": "#f0f6fc",      # Primary text (high contrast)
    "text_secondary": "#8b949e",    # Secondary text (medium contrast)
    "text_tertiary": "#6e7681",     # Tertiary text (low contrast)
    "text_placeholder": "#484f58",  # Placeholder text
    "text_disabled": "#484f58",     # Disabled text
    "text_inverse": "#24292f",      # Text on light backgrounds

    # === ACCENT COLORS ===
    "accent_primary": "#2f81f7",    # Primary accent (modern blue)
    "accent_secondary": "#a5a5a5",  # Secondary accent (neutral)
    "accent_success": "#3fb950",    # Success green
    "accent_warning": "#d29922",    # Warning amber
    "accent_error": "#f85149",      # Error red
    "accent_info": "#58a6ff",       # Info blue
    "accent_purple": "#bc8cff",     # Purple accent
    "accent_pink": "#f778ba",       # Pink accent

    # === INTERACTIVE STATES - ADOBE READER GRAY PALETTE ===
    "interactive_primary": "#6b7280",     # Primary buttons - professional gray
    "interactive_primary_hover": "#9ca3af", # Primary hover - lighter gray
    "interactive_primary_pressed": "#4b5563", # Primary pressed - darker gray
    "interactive_secondary": "#f3f4f6",    # Secondary buttons - light gray
    "interactive_secondary_hover": "#e5e7eb", # Secondary hover - medium light gray
    "interactive_secondary_pressed": "#d1d5db", # Secondary pressed - medium gray

    # === BORDERS ===
    "border_primary": "#30363d",    # Primary borders
    "border_secondary": "#21262d",  # Secondary borders
    "border_tertiary": "#373e47",   # Tertiary borders
    "border_focus": "#6b7280",      # Focus borders - professional gray
    "border_error": "#f85149",      # Error borders
    "border_success": "#3fb950",    # Success borders

    # === SHADOWS ===
    "shadow_small": "rgba(0, 0, 0, 0.12)",     # Small shadows
    "shadow_medium": "rgba(0, 0, 0, 0.16)",    # Medium shadows
    "shadow_large": "rgba(0, 0, 0, 0.24)",     # Large shadows
    "shadow_focus": "rgba(47, 129, 247, 0.4)", # Focus shadows

    # === OVERLAYS ===
    "overlay_light": "rgba(255, 255, 255, 0.05)",  # Light overlay
    "overlay_medium": "rgba(255, 255, 255, 0.1)",  # Medium overlay
    "overlay_dark": "rgba(0, 0, 0, 0.3)",          # Dark overlay
    "overlay_backdrop": "rgba(0, 0, 0, 0.5)",      # Modal backdrop

    # === GRADIENTS ===
    "gradient_primary": "linear-gradient(135deg, #2f81f7 0%, #1f6feb 100%)",
    "gradient_secondary": "linear-gradient(135deg, #21262d 0%, #30363d 100%)",
    "gradient_success": "linear-gradient(135deg, #3fb950 0%, #2ea043 100%)",
    "gradient_error": "linear-gradient(135deg, #f85149 0%, #da3633 100%)",

    # === LEGACY COMPATIBILITY ===
    "fg_primary": "#f0f6fc",        # Alias for text_primary
    "fg_secondary": "#8b949e",      # Alias for text_secondary
    "fg_tertiary": "#6e7681",       # Alias for text_tertiary
    "border": "#30363d",            # Alias for border_primary
    "hover": "#262c36",             # Alias for surface_hover
    "selected": "#4b5563",          # Alias for interactive_primary_pressed - dark gray
    "error": "#f85149",             # Alias for accent_error
    "warning": "#d29922",           # Alias for accent_warning
    "success": "#3fb950",           # Alias for accent_success
}

# === TYPOGRAPHY SYSTEM ===
TYPOGRAPHY = {
    # Font Families (fallback chain)
    "font_primary": ("Segoe UI", "SF Pro Display", "Inter", "system-ui", "sans-serif"),
    "font_secondary": ("Segoe UI", "SF Pro Text", "Inter", "system-ui", "sans-serif"),
    "font_mono": ("Cascadia Code", "SF Mono", "Consolas", "Monaco", "monospace"),

    # Font Sizes (in points)
    "size_xs": 10,      # Extra small text
    "size_sm": 11,      # Small text
    "size_base": 12,    # Base text size
    "size_md": 13,      # Medium text
    "size_lg": 14,      # Large text
    "size_xl": 16,      # Extra large text
    "size_2xl": 18,     # 2X large text
    "size_3xl": 20,     # 3X large text
    "size_4xl": 24,     # 4X large text

    # Font Weights
    "weight_light": "normal",
    "weight_normal": "normal",
    "weight_medium": "bold",
    "weight_semibold": "bold",
    "weight_bold": "bold",

    # Line Heights (multipliers)
    "leading_tight": 1.2,
    "leading_normal": 1.4,
    "leading_relaxed": 1.6,
    "leading_loose": 1.8,
}

# === SPACING SYSTEM ===
SPACING = {
    "xs": 2,    # 2px
    "sm": 4,    # 4px
    "md": 8,    # 8px
    "lg": 12,   # 12px
    "xl": 16,   # 16px
    "2xl": 20,  # 20px
    "3xl": 24,  # 24px
    "4xl": 32,  # 32px
    "5xl": 40,  # 40px
    "6xl": 48,  # 48px
}

# === BORDER RADIUS ===
RADIUS = {
    "none": 0,
    "sm": 3,
    "md": 6,
    "lg": 8,
    "xl": 12,
    "2xl": 16,
    "full": 9999,
}

# === COMPONENT SIZES ===
COMPONENT_SIZES = {
    "button_height_sm": 28,
    "button_height_md": 32,
    "button_height_lg": 36,
    "button_height_xl": 40,

    "input_height_sm": 28,
    "input_height_md": 32,
    "input_height_lg": 36,

    "toolbar_height": 44,
    "statusbar_height": 28,
    "sidebar_width": 280,
    "sidebar_min_width": 200,
    "sidebar_max_width": 400,
}

# === ANIMATION SETTINGS ===
ANIMATIONS = {
    "duration_fast": 150,      # 150ms
    "duration_normal": 250,    # 250ms
    "duration_slow": 350,      # 350ms
    "easing": "ease-out",
}

# File Extensions
SUPPORTED_EXTENSIONS = [".pdf"]

# Zoom Settings
MIN_ZOOM = 25
MAX_ZOOM = 500
ZOOM_STEP = 25

# Recent Files
MAX_RECENT_FILES = 10
