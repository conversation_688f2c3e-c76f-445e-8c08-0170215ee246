"""
Status bar component for ArchPDF - Shows current page, zoom level, and status information.
"""

import customtkinter as ctk
import logging

logger = logging.getLogger(__name__)

class StatusBar(ctk.CTkFrame):
    """Status bar showing page info, zoom level, and status messages."""
    
    def __init__(self, parent, app):
        """
        Initialize the modern status bar.

        Args:
            parent: Parent widget
            app: Main application instance
        """
        # Adobe Reader-style status bar
        super().__init__(
            parent,
            fg_color="#f3f4f6",      # Light gray background
            border_width=1,
            border_color="#d1d5db",  # Light gray border
            corner_radius=0,
            height=32
        )
        self.app = app

        # Create modern status bar elements
        self._create_modern_elements()
    
    def _create_modern_elements(self):
        """Create modern status bar elements with professional styling."""
        # Configure grid with proper spacing
        self.grid_columnconfigure(1, weight=1)  # Status message column expands
        self.grid_rowconfigure(0, weight=1)

        # === LEFT SECTION: Document Info ===
        self.left_frame = ctk.CTkFrame(
            self,
            fg_color="transparent",
            border_width=0
        )
        self.left_frame.grid(row=0, column=0, sticky="w", padx=(12, 8), pady=4)

        # Document icon and page info - Adobe Reader style
        self.doc_icon_label = ctk.CTkLabel(
            self.left_frame,
            text="⬜",                # Professional document icon
            text_color="#6b7280",    # Medium gray
            font=("Segoe UI", 12)
        )
        self.doc_icon_label.pack(side="left", padx=(0, 6))

        self.page_info_label = ctk.CTkLabel(
            self.left_frame,
            text="No document",
            text_color="#374151",    # Dark gray text
            font=("Segoe UI", 10, "normal")
        )
        self.page_info_label.pack(side="left")

        # === CENTER SECTION: Status Message ===
        self.center_frame = ctk.CTkFrame(
            self,
            fg_color="transparent",
            border_width=0
        )
        self.center_frame.grid(row=0, column=1, sticky="ew", padx=8, pady=4)

        # Status icon and message
        self.status_icon_label = ctk.CTkLabel(
            self.center_frame,
            text="✓",
            text_color="#3fb950",
            font=("Segoe UI", 11)
        )
        self.status_icon_label.pack(side="left", padx=(0, 6))

        self.status_label = ctk.CTkLabel(
            self.center_frame,
            text="Ready",
            text_color="#374151",    # Dark gray text
            font=("Segoe UI", 10, "normal")
        )
        self.status_label.pack(side="left")

        # === RIGHT SECTION: Zoom and File Info ===
        self.right_frame = ctk.CTkFrame(
            self,
            fg_color="transparent",
            border_width=0
        )
        self.right_frame.grid(row=0, column=2, sticky="e", padx=(8, 12), pady=4)

        # File size info - Adobe Reader style
        self.file_size_label = ctk.CTkLabel(
            self.right_frame,
            text="",
            text_color="#6b7280",    # Medium gray
            font=("Segoe UI", 9, "normal")
        )
        self.file_size_label.pack(side="left", padx=(0, 12))

        # Separator - Adobe Reader style
        self.separator = ctk.CTkLabel(
            self.right_frame,
            text="│",
            text_color="#d1d5db",    # Light gray separator
            font=("Segoe UI", 10)
        )
        self.separator.pack(side="left", padx=(0, 12))

        # Zoom icon and level - Adobe Reader style
        self.zoom_icon_label = ctk.CTkLabel(
            self.right_frame,
            text="⌕",                # Professional search/zoom icon
            text_color="#6b7280",    # Medium gray
            font=("Segoe UI", 10)
        )
        self.zoom_icon_label.pack(side="left", padx=(0, 4))

        self.zoom_info_label = ctk.CTkLabel(
            self.right_frame,
            text="100%",
            text_color="#374151",    # Dark gray text
            font=("Segoe UI", 10, "bold")
        )
        self.zoom_info_label.pack(side="left")
    
    def update_page_info(self, current_page: int, total_pages: int):
        """
        Update page information display with modern styling.

        Args:
            current_page: Current page number (1-based)
            total_pages: Total number of pages
        """
        if total_pages > 0:
            text = f"Page {current_page} of {total_pages}"
            self.doc_icon_label.configure(text="⬜", text_color="#3fb950")  # Green for loaded document
        else:
            text = "No document"
            self.doc_icon_label.configure(text="⬜", text_color="#6b7280")  # Gray for no document

        self.page_info_label.configure(text=text)

    def update_zoom_info(self, zoom_level: int):
        """
        Update zoom level display with enhanced styling.

        Args:
            zoom_level: Current zoom level percentage
        """
        self.zoom_info_label.configure(text=f"{zoom_level}%")

        # Change color based on zoom level - Adobe Reader style
        if zoom_level == 100:
            color = "#374151"  # Normal - dark gray
        elif zoom_level < 100:
            color = "#6b7280"  # Zoomed out - medium gray
        else:
            color = "#f85149"  # Zoomed in - red (keep for warning)

        self.zoom_info_label.configure(text_color=color)

    def set_status(self, message: str, status_type: str = "info"):
        """
        Set status message with icon and color coding.

        Args:
            message: Status message to display
            status_type: Type of status (info, success, warning, error)
        """
        # Set icon and color based on status type
        if status_type == "success":
            icon = "✓"
            icon_color = "#3fb950"
        elif status_type == "warning":
            icon = "⚠"
            icon_color = "#d29922"
        elif status_type == "error":
            icon = "✗"
            icon_color = "#f85149"
        else:  # info
            icon = "ℹ"
            icon_color = "#6b7280"  # Medium gray for info

        self.status_icon_label.configure(text=icon, text_color=icon_color)
        self.status_label.configure(text=message)
        logger.debug(f"Status ({status_type}): {message}")

    def clear_status(self):
        """Clear status message and reset to ready state."""
        self.status_icon_label.configure(text="✓", text_color="#3fb950")
        self.status_label.configure(text="Ready")

    def update_file_info(self, file_path: str = None, file_size: int = None):
        """
        Update file information display.

        Args:
            file_path: Path to the current file
            file_size: Size of the file in bytes
        """
        if file_size is not None:
            # Convert bytes to human readable format
            if file_size < 1024:
                size_text = f"{file_size} B"
            elif file_size < 1024 * 1024:
                size_text = f"{file_size / 1024:.1f} KB"
            else:
                size_text = f"{file_size / (1024 * 1024):.1f} MB"

            self.file_size_label.configure(text=size_text)
        else:
            self.file_size_label.configure(text="")
