"""
Icon management for ArchPDF - Handle bicolor icon set and icon operations.
"""

import tkinter as tk
from PIL import Image, ImageDraw, ImageTk
from pathlib import Path
import logging
from typing import Dict, Tu<PERSON>, Optional
from app.config import ICONS_DIR, ADOBE_READER_THEME

logger = logging.getLogger(__name__)

class IconManager:
    """Manages application icons and creates Adobe Reader DC style icon set."""

    def __init__(self, theme_manager):
        """
        Initialize icon manager.

        Args:
            theme_manager: Theme manager instance
        """
        self.theme_manager = theme_manager
        self.icon_cache: Dict[str, ImageTk.PhotoImage] = {}
        self.icon_size = (16, 16)  # Adobe Reader uses 16x16 for toolbar icons
        self.sidebar_icon_size = (14, 14)  # 14x14 for sidebar icons

        # Ensure icons directory exists
        ICONS_DIR.mkdir(parents=True, exist_ok=True)

        # Create Adobe Reader style icons if they don't exist
        self._create_adobe_reader_icons()
    
    def _create_adobe_reader_icons(self):
        """Create Adobe Reader DC style icons with thin lines and geometric design."""
        icons_to_create = [
            "open", "close", "save", "search", "zoom_in", "zoom_out",
            "zoom_fit", "previous", "next", "bookmark", "sidebar",
            "fullscreen", "settings", "help", "about", "print", "rotate_left", "rotate_right"
        ]

        for icon_name in icons_to_create:
            icon_path = ICONS_DIR / f"{icon_name}.png"
            if not icon_path.exists():
                self._create_adobe_reader_icon(icon_name, icon_path)
    
    def _create_adobe_reader_icon(self, icon_name: str, icon_path: Path):
        """
        Create an Adobe Reader DC style icon with thin lines and geometric design.

        Args:
            icon_name: Name of the icon
            icon_path: Path to save the icon
        """
        try:
            # Create image
            img = Image.new('RGBA', self.icon_size, (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)

            # Adobe Reader DC icon color - professional gray
            icon_color = "#5a5a5a"  # Exact Adobe Reader icon color
            icon_rgb = self._hex_to_rgb(icon_color)
            
            # Draw Adobe Reader style icon based on name
            if icon_name == "open":
                self._draw_adobe_open_icon(draw, icon_rgb)
            elif icon_name == "close":
                self._draw_adobe_close_icon(draw, icon_rgb)
            elif icon_name == "save":
                self._draw_adobe_save_icon(draw, icon_rgb)
            elif icon_name == "search":
                self._draw_adobe_search_icon(draw, icon_rgb)
            elif icon_name == "zoom_in":
                self._draw_adobe_zoom_in_icon(draw, icon_rgb)
            elif icon_name == "zoom_out":
                self._draw_adobe_zoom_out_icon(draw, icon_rgb)
            elif icon_name == "zoom_fit":
                self._draw_adobe_zoom_fit_icon(draw, icon_rgb)
            elif icon_name == "previous":
                self._draw_adobe_previous_icon(draw, icon_rgb)
            elif icon_name == "next":
                self._draw_adobe_next_icon(draw, icon_rgb)
            elif icon_name == "bookmark":
                self._draw_adobe_bookmark_icon(draw, icon_rgb)
            elif icon_name == "sidebar":
                self._draw_adobe_sidebar_icon(draw, icon_rgb)
            elif icon_name == "fullscreen":
                self._draw_adobe_fullscreen_icon(draw, icon_rgb)
            elif icon_name == "settings":
                self._draw_adobe_settings_icon(draw, icon_rgb)
            elif icon_name == "help":
                self._draw_adobe_help_icon(draw, icon_rgb)
            elif icon_name == "about":
                self._draw_adobe_about_icon(draw, icon_rgb)
            elif icon_name == "print":
                self._draw_adobe_print_icon(draw, icon_rgb)
            elif icon_name == "rotate_left":
                self._draw_adobe_rotate_left_icon(draw, icon_rgb)
            elif icon_name == "rotate_right":
                self._draw_adobe_rotate_right_icon(draw, icon_rgb)
            else:
                # Default Adobe Reader style icon
                self._draw_adobe_default_icon(draw, icon_rgb)
            
            # Save icon
            img.save(icon_path, "PNG")
            logger.debug(f"Created icon: {icon_name}")
            
        except Exception as e:
            logger.error(f"Failed to create icon {icon_name}: {e}")
    
    def _hex_to_rgb(self, hex_color: str) -> Tuple[int, int, int]:
        """Convert hex color to RGB tuple."""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def _draw_adobe_open_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style open folder icon with thin lines."""
        # Folder base - thin outline
        draw.rectangle([2, 6, 14, 13], outline=icon_rgb, width=1)
        # Folder tab - thin line
        draw.rectangle([2, 4, 8, 6], outline=icon_rgb, width=1)
        # Folder opening line
        draw.line([2, 6, 14, 6], fill=icon_rgb, width=1)

    def _draw_adobe_close_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style close/X icon with thin lines."""
        # X shape with thin lines
        draw.line([4, 4, 12, 12], fill=icon_rgb, width=1)
        draw.line([12, 4, 4, 12], fill=icon_rgb, width=1)

    def _draw_adobe_save_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style save icon with thin lines."""
        # Disk outline - thin
        draw.rectangle([2, 2, 14, 14], outline=icon_rgb, width=1)
        # Disk slot - thin line
        draw.rectangle([4, 3, 12, 5], outline=icon_rgb, width=1)
        # Disk center
        draw.rectangle([6, 7, 10, 11], outline=icon_rgb, width=1)

    def _draw_adobe_search_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style search icon with thin lines."""
        # Magnifying glass circle - thin
        draw.ellipse([2, 2, 10, 10], outline=icon_rgb, width=1)
        # Handle - thin line
        draw.line([9, 9, 14, 14], fill=icon_rgb, width=1)
    
    def _draw_adobe_zoom_in_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style zoom in icon with thin lines."""
        # Magnifying glass - thin circle
        draw.ellipse([2, 2, 10, 10], outline=icon_rgb, width=1)
        # Plus sign - thin lines
        draw.line([6, 4, 6, 8], fill=icon_rgb, width=1)
        draw.line([4, 6, 8, 6], fill=icon_rgb, width=1)
        # Handle - thin line
        draw.line([9, 9, 14, 14], fill=icon_rgb, width=1)

    def _draw_adobe_zoom_out_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style zoom out icon with thin lines."""
        # Magnifying glass - thin circle
        draw.ellipse([2, 2, 10, 10], outline=icon_rgb, width=1)
        # Minus sign - thin line
        draw.line([4, 6, 8, 6], fill=icon_rgb, width=1)
        # Handle - thin line
        draw.line([9, 9, 14, 14], fill=icon_rgb, width=1)

    def _draw_adobe_zoom_fit_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style zoom fit icon with thin lines."""
        # Rectangle - thin outline
        draw.rectangle([3, 4, 13, 12], outline=icon_rgb, width=1)
        # Corner brackets - thin lines
        draw.line([1, 6, 3, 6], fill=icon_rgb, width=1)
        draw.line([13, 6, 15, 6], fill=icon_rgb, width=1)
        draw.line([1, 10, 3, 10], fill=icon_rgb, width=1)
        draw.line([13, 10, 15, 10], fill=icon_rgb, width=1)

    def _draw_adobe_previous_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style previous/left arrow with thin lines."""
        # Left arrow - thin lines
        draw.line([10, 4, 6, 8], fill=icon_rgb, width=1)
        draw.line([6, 8, 10, 12], fill=icon_rgb, width=1)

    def _draw_adobe_next_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style next/right arrow with thin lines."""
        # Right arrow - thin lines
        draw.line([6, 4, 10, 8], fill=icon_rgb, width=1)
        draw.line([10, 8, 6, 12], fill=icon_rgb, width=1)
    
    def _draw_adobe_bookmark_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style bookmark icon with thin lines."""
        # Bookmark shape - thin outline
        points = [(5, 2), (11, 2), (11, 14), (8, 11), (5, 14)]
        draw.polygon(points, outline=icon_rgb, width=1)

    def _draw_adobe_sidebar_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style sidebar icon with thin lines."""
        # Sidebar rectangle - thin outline
        draw.rectangle([2, 3, 6, 13], outline=icon_rgb, width=1)
        # Main area - thin outline
        draw.rectangle([8, 3, 14, 13], outline=icon_rgb, width=1)
        # Content lines in main area
        draw.line([9, 5, 13, 5], fill=icon_rgb, width=1)
        draw.line([9, 7, 13, 7], fill=icon_rgb, width=1)
        draw.line([9, 9, 13, 9], fill=icon_rgb, width=1)

    def _draw_adobe_fullscreen_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style fullscreen icon with thin lines."""
        # Corner brackets - thin lines
        draw.line([2, 2, 2, 5], fill=icon_rgb, width=1)
        draw.line([2, 2, 5, 2], fill=icon_rgb, width=1)
        draw.line([14, 2, 14, 5], fill=icon_rgb, width=1)
        draw.line([14, 2, 11, 2], fill=icon_rgb, width=1)
        draw.line([2, 14, 2, 11], fill=icon_rgb, width=1)
        draw.line([2, 14, 5, 14], fill=icon_rgb, width=1)
        draw.line([14, 14, 14, 11], fill=icon_rgb, width=1)
        draw.line([14, 14, 11, 14], fill=icon_rgb, width=1)
    
    def _draw_adobe_settings_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style settings/gear icon with thin lines."""
        # Gear shape - thin circle
        draw.ellipse([4, 4, 12, 12], outline=icon_rgb, width=1)
        # Inner circle
        draw.ellipse([6, 6, 10, 10], outline=icon_rgb, width=1)
        # Gear teeth - thin lines
        draw.line([8, 2, 8, 4], fill=icon_rgb, width=1)  # Top
        draw.line([8, 12, 8, 14], fill=icon_rgb, width=1)  # Bottom
        draw.line([2, 8, 4, 8], fill=icon_rgb, width=1)  # Left
        draw.line([12, 8, 14, 8], fill=icon_rgb, width=1)  # Right

    def _draw_adobe_help_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style help/question mark icon with thin lines."""
        # Circle - thin outline
        draw.ellipse([2, 2, 14, 14], outline=icon_rgb, width=1)
        # Question mark - thin lines
        draw.arc([5, 4, 11, 9], 0, 180, fill=icon_rgb, width=1)
        draw.line([8, 9, 8, 10], fill=icon_rgb, width=1)
        draw.rectangle([7, 11, 9, 12], fill=icon_rgb)

    def _draw_adobe_about_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style about/info icon with thin lines."""
        # Circle - thin outline
        draw.ellipse([2, 2, 14, 14], outline=icon_rgb, width=1)
        # i - thin lines
        draw.rectangle([7, 5, 9, 6], fill=icon_rgb)
        draw.rectangle([7, 7, 9, 12], fill=icon_rgb)

    def _draw_adobe_print_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style print icon with thin lines."""
        # Printer body - thin outline
        draw.rectangle([2, 6, 14, 12], outline=icon_rgb, width=1)
        # Paper tray - thin outline
        draw.rectangle([4, 4, 12, 6], outline=icon_rgb, width=1)
        # Paper output - thin outline
        draw.rectangle([4, 12, 12, 14], outline=icon_rgb, width=1)

    def _draw_adobe_rotate_left_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style rotate left icon with thin lines."""
        # Circular arrow - thin arc
        draw.arc([3, 3, 13, 13], 45, 315, fill=icon_rgb, width=1)
        # Arrow head - thin lines
        draw.line([4, 6, 6, 4], fill=icon_rgb, width=1)
        draw.line([4, 6, 6, 8], fill=icon_rgb, width=1)

    def _draw_adobe_rotate_right_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style rotate right icon with thin lines."""
        # Circular arrow - thin arc
        draw.arc([3, 3, 13, 13], 225, 135, fill=icon_rgb, width=1)
        # Arrow head - thin lines
        draw.line([12, 6, 10, 4], fill=icon_rgb, width=1)
        draw.line([12, 6, 10, 8], fill=icon_rgb, width=1)

    def _draw_adobe_default_icon(self, draw, icon_rgb):
        """Draw Adobe Reader style default icon with thin lines."""
        # Simple rectangle - thin outline
        draw.rectangle([4, 4, 12, 12], outline=icon_rgb, width=1)
    
    def get_icon(self, icon_name: str, size: Optional[Tuple[int, int]] = None) -> Optional[ImageTk.PhotoImage]:
        """
        Get an icon by name.
        
        Args:
            icon_name: Name of the icon
            size: Optional size tuple (width, height)
            
        Returns:
            PhotoImage or None if not found
        """
        if size is None:
            size = self.icon_size
        
        cache_key = f"{icon_name}_{size[0]}x{size[1]}"
        
        if cache_key in self.icon_cache:
            return self.icon_cache[cache_key]
        
        try:
            icon_path = ICONS_DIR / f"{icon_name}.png"
            
            if icon_path.exists():
                img = Image.open(icon_path)
                if img.size != size:
                    img = img.resize(size, Image.Resampling.LANCZOS)
                
                photo = ImageTk.PhotoImage(img)
                self.icon_cache[cache_key] = photo
                return photo
            else:
                logger.warning(f"Icon not found: {icon_name}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to load icon {icon_name}: {e}")
            return None
    
    def clear_cache(self):
        """Clear icon cache."""
        self.icon_cache.clear()
        logger.debug("Icon cache cleared")
