"""
Toolbar component for ArchPDF - Main toolbar with common actions.
"""

import tkinter as tk
import customtkinter as ctk
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class ToolBar(ctk.CTkFrame):
    """Main toolbar with common PDF operations."""
    
    def __init__(self, parent, app):
        """
        Initialize the modern toolbar.

        Args:
            parent: Parent widget
            app: Main application instance
        """
        # Use modern frame styling
        super().__init__(parent, **app.theme_manager.get_frame_style("secondary"))
        self.app = app

        # Configure toolbar height
        self.configure(height=app.theme_manager.sizes["toolbar_height"])

        # Create modern toolbar buttons
        self._create_modern_buttons()

        # Initial state
        self.update_state(False)
    
    def _create_modern_buttons(self):
        """Create Adobe Reader DC exact toolbar buttons with precise styling."""
        # Configure grid with Adobe Reader spacing
        self.grid_columnconfigure(100, weight=1)  # Spacer column for right alignment

        # === FILE OPERATIONS GROUP ===
        # Adobe Reader DC exact Open button styling
        self.open_btn = ctk.CTkButton(
            self,
            text="📁 Open",              # Adobe Reader style folder icon
            command=self.app.open_file,
            width=80,
            height=32,                   # Adobe Reader toolbar height: 40px, button: 32px
            fg_color="#5a5a5a",         # Adobe Reader exact button gray
            hover_color="#707070",      # Adobe Reader exact hover gray
            text_color="#ffffff",
            corner_radius=3,            # Adobe Reader uses smaller radius
            font=("Segoe UI", 11, "normal"),
            border_width=0
        )
        self.open_btn.grid(row=0, column=0, padx=(8, 4), pady=4)

        # Adobe Reader DC exact separator - thin gray line
        sep1 = ctk.CTkFrame(
            self,
            width=1,
            height=24,
            fg_color="#d0d0d0",      # Adobe Reader exact separator color
            corner_radius=0
        )
        sep1.grid(row=0, column=1, sticky="ns", padx=6, pady=8)

        # === NAVIGATION GROUP ===
        # Adobe Reader-style navigation buttons with professional icons
        self.prev_btn = ctk.CTkButton(
            self,
            text="◁",                # Professional left triangle
            command=self.app.previous_page,
            width=36,
            height=36,
            fg_color="#f3f4f6",      # Very light gray background
            hover_color="#e5e7eb",   # Slightly darker on hover
            text_color="#374151",    # Dark gray text
            corner_radius=6,
            font=("Segoe UI", 14, "normal"),
            border_width=1,
            border_color="#d1d5db"   # Light border
        )
        self.prev_btn.grid(row=0, column=2, padx=3, pady=6)

        # Adobe Reader-style page navigation
        self.page_entry = ctk.CTkEntry(
            self,
            width=65,
            height=36,
            justify="center",
            fg_color="#ffffff",      # White background like Adobe Reader
            border_color="#d1d5db",  # Light gray border
            text_color="#374151",    # Dark gray text
            placeholder_text_color="#9ca3af",
            font=("Segoe UI", 12, "normal"),
            corner_radius=6,
            border_width=1
        )
        self.page_entry.grid(row=0, column=3, padx=6, pady=6)
        self.page_entry.bind("<Return>", self._on_page_entry)

        self.page_label = ctk.CTkLabel(
            self,
            text="/ 0",
            text_color="#6b7280",    # Medium gray text
            font=("Segoe UI", 12, "normal")
        )
        self.page_label.grid(row=0, column=4, padx=(3, 12), pady=6)

        self.next_btn = ctk.CTkButton(
            self,
            text="▷",                # Professional right triangle
            command=self.app.next_page,
            width=36,
            height=36,
            fg_color="#f3f4f6",      # Very light gray background
            hover_color="#e5e7eb",   # Slightly darker on hover
            text_color="#374151",    # Dark gray text
            corner_radius=6,
            font=("Segoe UI", 14, "normal"),
            border_width=1,
            border_color="#d1d5db"   # Light border
        )
        self.next_btn.grid(row=0, column=5, padx=3, pady=6)

        # Adobe Reader-style separator
        sep2 = ctk.CTkFrame(
            self,
            width=1,
            height=24,
            fg_color="#d1d5db",      # Light gray separator
            corner_radius=0
        )
        sep2.grid(row=0, column=6, sticky="ns", padx=8, pady=6)

        # === ZOOM CONTROLS GROUP ===
        # Adobe Reader-style zoom controls
        self.zoom_out_btn = ctk.CTkButton(
            self,
            text="−",
            command=self.app.zoom_out,
            width=36,
            height=36,
            fg_color="#f3f4f6",      # Very light gray background
            hover_color="#e5e7eb",   # Slightly darker on hover
            text_color="#374151",    # Dark gray text
            corner_radius=6,
            font=("Segoe UI", 16, "normal"),
            border_width=1,
            border_color="#d1d5db"   # Light border
        )
        self.zoom_out_btn.grid(row=0, column=7, padx=3, pady=6)

        # Adobe Reader-style zoom display
        self.zoom_label = ctk.CTkLabel(
            self,
            text="100%",
            text_color="#374151",    # Dark gray text
            font=("Segoe UI", 12, "bold"),
            width=55,
            height=36,
            fg_color="#ffffff",      # White background
            corner_radius=6
        )
        self.zoom_label.grid(row=0, column=8, padx=6, pady=6)

        self.zoom_in_btn = ctk.CTkButton(
            self,
            text="+",
            command=self.app.zoom_in,
            width=36,
            height=36,
            fg_color="#f3f4f6",      # Very light gray background
            hover_color="#e5e7eb",   # Slightly darker on hover
            text_color="#374151",    # Dark gray text
            corner_radius=6,
            font=("Segoe UI", 16, "normal"),
            border_width=1,
            border_color="#d1d5db"   # Light border
        )
        self.zoom_in_btn.grid(row=0, column=9, padx=3, pady=6)

        # === FIT CONTROLS GROUP ===
        # Adobe Reader-style fit controls with professional icons
        self.fit_width_btn = ctk.CTkButton(
            self,
            text="⟷ Fit Width",      # Professional horizontal arrows
            command=self.app.zoom_fit_width,
            width=95,
            height=36,
            fg_color="transparent",
            hover_color="#f3f4f6",   # Light gray hover
            text_color="#6b7280",    # Medium gray text
            corner_radius=6,
            font=("Segoe UI", 11, "normal"),
            border_width=0           # No border for clean look
        )
        self.fit_width_btn.grid(row=0, column=10, padx=6, pady=6)

        self.fit_page_btn = ctk.CTkButton(
            self,
            text="⬜ Fit Page",       # Professional square icon
            command=self.app.zoom_fit_page,
            width=95,
            height=36,
            fg_color="transparent",
            hover_color="#f3f4f6",   # Light gray hover
            text_color="#6b7280",    # Medium gray text
            corner_radius=6,
            font=("Segoe UI", 11, "normal"),
            border_width=0           # No border for clean look
        )
        self.fit_page_btn.grid(row=0, column=11, padx=3, pady=6)

        # Adobe Reader-style separator
        sep3 = ctk.CTkFrame(
            self,
            width=1,
            height=24,
            fg_color="#d1d5db",      # Light gray separator
            corner_radius=0
        )
        sep3.grid(row=0, column=12, sticky="ns", padx=8, pady=6)

        # === SEARCH GROUP ===
        # Adobe Reader-style search button with professional icon
        self.search_btn = ctk.CTkButton(
            self,
            text="⌕ Search",         # Professional search icon
            command=self.app.show_search_dialog,
            width=100,
            height=36,
            fg_color="#f3f4f6",      # Very light gray background
            hover_color="#e5e7eb",   # Slightly darker on hover
            text_color="#374151",    # Dark gray text
            corner_radius=6,
            font=("Segoe UI", 11, "normal"),
            border_width=1,
            border_color="#d1d5db"   # Light border
        )
        self.search_btn.grid(row=0, column=13, padx=6, pady=6)

        # Spacer (column 100 has weight=1)

        # === VIEW CONTROLS GROUP ===
        # Adobe Reader-style sidebar toggle with professional icon
        self.sidebar_btn = ctk.CTkButton(
            self,
            text="⫸ Sidebar",        # Professional sidebar icon
            command=self.app.toggle_sidebar,
            width=100,
            height=36,
            fg_color="transparent",
            hover_color="#f3f4f6",   # Light gray hover
            text_color="#6b7280",    # Medium gray text
            corner_radius=6,
            font=("Segoe UI", 11, "normal"),
            border_width=0           # No border for clean look
        )
        self.sidebar_btn.grid(row=0, column=101, padx=(6, 12), pady=6)
    
    def _on_page_entry(self, event):
        """Handle page entry return key."""
        try:
            page_num = int(self.page_entry.get())
            self.app.go_to_page(page_num)
        except ValueError:
            # Invalid page number, reset to current page
            if self.app.pdf_engine.is_document_loaded():
                current_page = self.app.pdf_engine.get_current_page() + 1
                self.page_entry.delete(0, "end")
                self.page_entry.insert(0, str(current_page))
    
    def update_state(self, has_document: bool):
        """
        Update toolbar state based on document availability.
        
        Args:
            has_document: Whether a document is loaded
        """
        # Navigation buttons
        self.prev_btn.configure(state="normal" if has_document else "disabled")
        self.next_btn.configure(state="normal" if has_document else "disabled")
        
        # Page entry
        self.page_entry.configure(state="normal" if has_document else "disabled")
        
        # Zoom controls
        self.zoom_out_btn.configure(state="normal" if has_document else "disabled")
        self.zoom_in_btn.configure(state="normal" if has_document else "disabled")
        self.fit_width_btn.configure(state="normal" if has_document else "disabled")
        self.fit_page_btn.configure(state="normal" if has_document else "disabled")
        
        # Search
        self.search_btn.configure(state="normal" if has_document else "disabled")
        
        # Update page info
        if has_document:
            current_page = self.app.pdf_engine.get_current_page() + 1
            total_pages = self.app.pdf_engine.page_count
            
            self.page_entry.delete(0, "end")
            self.page_entry.insert(0, str(current_page))
            self.page_label.configure(text=f"/ {total_pages}")
            
            # Update zoom level
            zoom_level = self.app.pdf_viewer.get_zoom_level()
            self.zoom_label.configure(text=f"{zoom_level}%")
        else:
            self.page_entry.delete(0, "end")
            self.page_label.configure(text="/ 0")
            self.zoom_label.configure(text="100%")
    
    def update_page_info(self, current_page: int, total_pages: int):
        """
        Update page information display.
        
        Args:
            current_page: Current page number (1-based)
            total_pages: Total number of pages
        """
        self.page_entry.delete(0, "end")
        self.page_entry.insert(0, str(current_page))
        self.page_label.configure(text=f"/ {total_pages}")
    
    def update_zoom_info(self, zoom_level: int):
        """
        Update zoom level display.
        
        Args:
            zoom_level: Current zoom level percentage
        """
        self.zoom_label.configure(text=f"{zoom_level}%")
