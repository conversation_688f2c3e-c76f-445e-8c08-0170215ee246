"""
Toolbar component for ArchPDF - Main toolbar with common actions.
"""

import tkinter as tk
import customtkinter as ctk
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class ToolBar(ctk.CTkFrame):
    """Main toolbar with common PDF operations."""
    
    def __init__(self, parent, app):
        """
        Initialize the Adobe Reader DC exact toolbar.

        Args:
            parent: Parent widget
            app: Main application instance
        """
        # Adobe Reader DC exact toolbar styling
        super().__init__(
            parent,
            fg_color="#e5e5e5",      # Adobe Reader exact toolbar background
            corner_radius=0,         # Adobe Reader uses no corner radius
            border_width=0           # No border
        )
        self.app = app

        # Configure Adobe Reader DC exact toolbar height (40px)
        self.configure(height=40)

        # Create modern toolbar buttons
        self._create_modern_buttons()

        # Initial state
        self.update_state(False)
    
    def _create_modern_buttons(self):
        """Create Adobe Reader DC exact toolbar buttons with precise styling."""
        # Configure grid with Adobe Reader spacing
        self.grid_columnconfigure(100, weight=1)  # Spacer column for right alignment

        # === FILE OPERATIONS GROUP ===
        # Adobe Reader DC exact Open button styling
        self.open_btn = ctk.CTkButton(
            self,
            text="📁 Open",              # Adobe Reader style folder icon
            command=self.app.open_file,
            width=80,
            height=32,                   # Adobe Reader toolbar height: 40px, button: 32px
            fg_color="#5a5a5a",         # Adobe Reader exact button gray
            hover_color="#707070",      # Adobe Reader exact hover gray
            text_color="#ffffff",
            corner_radius=3,            # Adobe Reader uses smaller radius
            font=("Segoe UI", 11, "normal"),
            border_width=0
        )
        self.open_btn.grid(row=0, column=0, padx=(8, 4), pady=4)

        # Adobe Reader DC exact separator - thin gray line
        sep1 = ctk.CTkFrame(
            self,
            width=1,
            height=24,
            fg_color="#d0d0d0",      # Adobe Reader exact separator color
            corner_radius=0
        )
        sep1.grid(row=0, column=1, sticky="ns", padx=6, pady=8)

        # === NAVIGATION GROUP ===
        # Adobe Reader DC exact navigation buttons
        self.prev_btn = ctk.CTkButton(
            self,
            text="◀",                # Adobe Reader style left arrow
            command=self.app.previous_page,
            width=32,
            height=32,
            fg_color="#f0f0f0",      # Adobe Reader exact light gray background
            hover_color="#e8e8e8",   # Adobe Reader exact hover
            text_color="#5a5a5a",    # Adobe Reader exact icon color
            corner_radius=3,
            font=("Segoe UI", 12, "normal"),
            border_width=1,
            border_color="#d0d0d0"   # Adobe Reader exact border
        )
        self.prev_btn.grid(row=0, column=2, padx=2, pady=4)

        # Adobe Reader DC exact page navigation
        self.page_entry = ctk.CTkEntry(
            self,
            width=50,
            height=32,
            justify="center",
            fg_color="#ffffff",      # Adobe Reader exact white background
            border_color="#d0d0d0",  # Adobe Reader exact border color
            text_color="#2c2c2c",    # Adobe Reader exact text color
            placeholder_text_color="#8a8a8a",
            font=("Segoe UI", 11, "normal"),
            corner_radius=3,
            border_width=1
        )
        self.page_entry.grid(row=0, column=3, padx=4, pady=4)
        self.page_entry.bind("<Return>", self._on_page_entry)

        self.page_label = ctk.CTkLabel(
            self,
            text="/ 0",
            text_color="#5a5a5a",    # Adobe Reader exact medium gray text
            font=("Segoe UI", 11, "normal")
        )
        self.page_label.grid(row=0, column=4, padx=(2, 8), pady=4)

        self.next_btn = ctk.CTkButton(
            self,
            text="▶",                # Adobe Reader style right arrow
            command=self.app.next_page,
            width=32,
            height=32,
            fg_color="#f0f0f0",      # Adobe Reader exact light gray background
            hover_color="#e8e8e8",   # Adobe Reader exact hover
            text_color="#5a5a5a",    # Adobe Reader exact icon color
            corner_radius=3,
            font=("Segoe UI", 12, "normal"),
            border_width=1,
            border_color="#d0d0d0"   # Adobe Reader exact border
        )
        self.next_btn.grid(row=0, column=5, padx=2, pady=4)

        # Adobe Reader DC exact separator
        sep2 = ctk.CTkFrame(
            self,
            width=1,
            height=24,
            fg_color="#d0d0d0",      # Adobe Reader exact separator color
            corner_radius=0
        )
        sep2.grid(row=0, column=6, sticky="ns", padx=6, pady=8)

        # === ZOOM CONTROLS GROUP ===
        # Adobe Reader DC exact zoom controls
        self.zoom_out_btn = ctk.CTkButton(
            self,
            text="−",
            command=self.app.zoom_out,
            width=32,
            height=32,
            fg_color="#f0f0f0",      # Adobe Reader exact light gray background
            hover_color="#e8e8e8",   # Adobe Reader exact hover
            text_color="#5a5a5a",    # Adobe Reader exact icon color
            corner_radius=3,
            font=("Segoe UI", 14, "normal"),
            border_width=1,
            border_color="#d0d0d0"   # Adobe Reader exact border
        )
        self.zoom_out_btn.grid(row=0, column=7, padx=2, pady=4)

        # Adobe Reader DC exact zoom display
        self.zoom_label = ctk.CTkLabel(
            self,
            text="100%",
            text_color="#2c2c2c",    # Adobe Reader exact dark gray text
            font=("Segoe UI", 11, "normal"),
            width=45,
            height=32,
            fg_color="#ffffff",      # Adobe Reader exact white background
            corner_radius=3
        )
        self.zoom_label.grid(row=0, column=8, padx=4, pady=4)

        self.zoom_in_btn = ctk.CTkButton(
            self,
            text="+",
            command=self.app.zoom_in,
            width=32,
            height=32,
            fg_color="#f0f0f0",      # Adobe Reader exact light gray background
            hover_color="#e8e8e8",   # Adobe Reader exact hover
            text_color="#5a5a5a",    # Adobe Reader exact icon color
            corner_radius=3,
            font=("Segoe UI", 14, "normal"),
            border_width=1,
            border_color="#d0d0d0"   # Adobe Reader exact border
        )
        self.zoom_in_btn.grid(row=0, column=9, padx=2, pady=4)

        # === FIT CONTROLS GROUP ===
        # Adobe Reader DC exact fit controls
        self.fit_width_btn = ctk.CTkButton(
            self,
            text="↔ Fit Width",      # Adobe Reader style horizontal arrows
            command=self.app.zoom_fit_width,
            width=80,
            height=32,
            fg_color="transparent",
            hover_color="#f0f0f0",   # Adobe Reader exact light gray hover
            text_color="#5a5a5a",    # Adobe Reader exact medium gray text
            corner_radius=3,
            font=("Segoe UI", 10, "normal"),
            border_width=0           # No border for clean look
        )
        self.fit_width_btn.grid(row=0, column=10, padx=4, pady=4)

        self.fit_page_btn = ctk.CTkButton(
            self,
            text="⬛ Fit Page",       # Adobe Reader style square icon
            command=self.app.zoom_fit_page,
            width=80,
            height=32,
            fg_color="transparent",
            hover_color="#f0f0f0",   # Adobe Reader exact light gray hover
            text_color="#5a5a5a",    # Adobe Reader exact medium gray text
            corner_radius=3,
            font=("Segoe UI", 10, "normal"),
            border_width=0           # No border for clean look
        )
        self.fit_page_btn.grid(row=0, column=11, padx=2, pady=4)

        # Adobe Reader DC exact separator
        sep3 = ctk.CTkFrame(
            self,
            width=1,
            height=24,
            fg_color="#d0d0d0",      # Adobe Reader exact separator color
            corner_radius=0
        )
        sep3.grid(row=0, column=12, sticky="ns", padx=6, pady=8)

        # === SEARCH GROUP ===
        # Adobe Reader DC exact search button
        self.search_btn = ctk.CTkButton(
            self,
            text="🔍 Search",         # Adobe Reader style search icon
            command=self.app.show_search_dialog,
            width=80,
            height=32,
            fg_color="#f0f0f0",      # Adobe Reader exact light gray background
            hover_color="#e8e8e8",   # Adobe Reader exact hover
            text_color="#5a5a5a",    # Adobe Reader exact text color
            corner_radius=3,
            font=("Segoe UI", 10, "normal"),
            border_width=1,
            border_color="#d0d0d0"   # Adobe Reader exact border
        )
        self.search_btn.grid(row=0, column=13, padx=4, pady=4)

        # Spacer (column 100 has weight=1)

        # === VIEW CONTROLS GROUP ===
        # Adobe Reader DC exact sidebar toggle
        self.sidebar_btn = ctk.CTkButton(
            self,
            text="📋 Sidebar",        # Adobe Reader style sidebar icon
            command=self.app.toggle_sidebar,
            width=80,
            height=32,
            fg_color="transparent",
            hover_color="#f0f0f0",   # Adobe Reader exact light gray hover
            text_color="#5a5a5a",    # Adobe Reader exact medium gray text
            corner_radius=3,
            font=("Segoe UI", 10, "normal"),
            border_width=0           # No border for clean look
        )
        self.sidebar_btn.grid(row=0, column=101, padx=(4, 8), pady=4)
    
    def _on_page_entry(self, event):
        """Handle page entry return key."""
        try:
            page_num = int(self.page_entry.get())
            self.app.go_to_page(page_num)
        except ValueError:
            # Invalid page number, reset to current page
            if self.app.pdf_engine.is_document_loaded():
                current_page = self.app.pdf_engine.get_current_page() + 1
                self.page_entry.delete(0, "end")
                self.page_entry.insert(0, str(current_page))
    
    def update_state(self, has_document: bool):
        """
        Update toolbar state based on document availability.
        
        Args:
            has_document: Whether a document is loaded
        """
        # Navigation buttons
        self.prev_btn.configure(state="normal" if has_document else "disabled")
        self.next_btn.configure(state="normal" if has_document else "disabled")
        
        # Page entry
        self.page_entry.configure(state="normal" if has_document else "disabled")
        
        # Zoom controls
        self.zoom_out_btn.configure(state="normal" if has_document else "disabled")
        self.zoom_in_btn.configure(state="normal" if has_document else "disabled")
        self.fit_width_btn.configure(state="normal" if has_document else "disabled")
        self.fit_page_btn.configure(state="normal" if has_document else "disabled")
        
        # Search
        self.search_btn.configure(state="normal" if has_document else "disabled")
        
        # Update page info
        if has_document:
            current_page = self.app.pdf_engine.get_current_page() + 1
            total_pages = self.app.pdf_engine.page_count
            
            self.page_entry.delete(0, "end")
            self.page_entry.insert(0, str(current_page))
            self.page_label.configure(text=f"/ {total_pages}")
            
            # Update zoom level
            zoom_level = self.app.pdf_viewer.get_zoom_level()
            self.zoom_label.configure(text=f"{zoom_level}%")
        else:
            self.page_entry.delete(0, "end")
            self.page_label.configure(text="/ 0")
            self.zoom_label.configure(text="100%")
    
    def update_page_info(self, current_page: int, total_pages: int):
        """
        Update page information display.
        
        Args:
            current_page: Current page number (1-based)
            total_pages: Total number of pages
        """
        self.page_entry.delete(0, "end")
        self.page_entry.insert(0, str(current_page))
        self.page_label.configure(text=f"/ {total_pages}")
    
    def update_zoom_info(self, zoom_level: int):
        """
        Update zoom level display.
        
        Args:
            zoom_level: Current zoom level percentage
        """
        self.zoom_label.configure(text=f"{zoom_level}%")
